<?php
/**
 * ChatGABI Theme Functions
 *
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Theme constants - Updated to force cache refresh
define('CHATGABI_VERSION', '1.0.2');
define('CHATGABI_THEME_DIR', get_template_directory());
define('CHATGABI_THEME_URL', get_template_directory_uri());

/**
 * Memory optimization: Increase memory limit if needed
 */
function chatgabi_optimize_memory() {
    $current_limit = ini_get('memory_limit');
    $current_bytes = wp_convert_hr_to_bytes($current_limit);
    $required_bytes = 768 * 1024 * 1024; // 768MB

    if ($current_bytes < $required_bytes) {
        ini_set('memory_limit', '768M');
    }

    // Enable garbage collection
    if (function_exists('gc_enable')) {
        gc_enable();
    }
}
add_action('init', 'chatgabi_optimize_memory', 1);

/**
 * Initialize session for location detection (optimized)
 */
function chatgabi_init_session() {
    // Only start session if needed
    if (!session_id() && (is_page() || is_front_page())) {
        session_start();
    }
}
add_action('init', 'chatgabi_init_session', 5);

/**
 * Theme setup
 */
function chatgabi_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ));
    add_theme_support('wp-block-styles');
    add_theme_support('align-wide');
    add_theme_support('editor-styles');
    add_editor_style('assets/css/editor-style.css');

    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'chatgabi'),
        'footer' => __('Footer Menu', 'chatgabi'),
    ));

    // Set content width
    $GLOBALS['content_width'] = 1200;
}
add_action('after_setup_theme', 'chatgabi_setup');

/**
 * Create required pages on theme activation
 */
function chatgabi_create_required_pages() {
    // Check if preferences page exists
    $preferences_page = get_page_by_path('preferences');

    if (!$preferences_page) {
        // Create preferences page
        $preferences_page_data = array(
            'post_title' => 'User Preferences',
            'post_content' => '',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'preferences',
            'page_template' => 'page-preferences.php'
        );

        $preferences_page_id = wp_insert_post($preferences_page_data);

        if ($preferences_page_id) {
            // Set the page template
            update_post_meta($preferences_page_id, '_wp_page_template', 'page-preferences.php');
        }
    }

    // Create Templates page
    $templates_page = get_page_by_path('templates');
    if (!$templates_page) {
        $templates_page_id = wp_insert_post(array(
            'post_title' => 'ChatGABI Templates',
            'post_content' => 'AI-powered business templates for African entrepreneurs.',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'templates'
        ));

        if ($templates_page_id) {
            // Set the page template
            update_post_meta($templates_page_id, '_wp_page_template', 'page-templates.php');
        }
    }

    // Create sample templates
    chatgabi_create_sample_templates();
}
add_action('after_switch_theme', 'chatgabi_create_required_pages');

/**
 * Create sample templates for users to see
 */
function chatgabi_create_sample_templates() {
    global $wpdb;

    // Check if tables exist
    if (!function_exists('chatgabi_check_templates_tables_exist') || !chatgabi_check_templates_tables_exist()) {
        return;
    }

    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $categories_table = $wpdb->prefix . 'chatgabi_template_categories';

    // Check if sample templates already exist
    $existing_samples = $wpdb->get_var("SELECT COUNT(*) FROM {$templates_table} WHERE user_id = 0 AND is_public = 1");
    if ($existing_samples > 0) {
        return; // Sample templates already exist
    }

    // Get or create categories
    $business_plan_cat = $wpdb->get_var("SELECT id FROM {$categories_table} WHERE slug = 'business-plan'");
    $marketing_cat = $wpdb->get_var("SELECT id FROM {$categories_table} WHERE slug = 'marketing'");
    $financial_cat = $wpdb->get_var("SELECT id FROM {$categories_table} WHERE slug = 'financial'");

    // Create categories if they don't exist
    if (!$business_plan_cat) {
        $wpdb->insert($categories_table, array(
            'name' => 'Business Plans',
            'slug' => 'business-plan',
            'description' => 'Comprehensive business plan templates',
            'icon' => '📋',
            'color' => '#007cba',
            'sort_order' => 1
        ));
        $business_plan_cat = $wpdb->insert_id;
    }

    if (!$marketing_cat) {
        $wpdb->insert($categories_table, array(
            'name' => 'Marketing Strategies',
            'slug' => 'marketing',
            'description' => 'Marketing and promotion templates',
            'icon' => '📈',
            'color' => '#00a32a',
            'sort_order' => 2
        ));
        $marketing_cat = $wpdb->insert_id;
    }

    if (!$financial_cat) {
        $wpdb->insert($categories_table, array(
            'name' => 'Financial Forecasts',
            'slug' => 'financial',
            'description' => 'Financial planning and forecasting templates',
            'icon' => '💰',
            'color' => '#d63638',
            'sort_order' => 3
        ));
        $financial_cat = $wpdb->insert_id;
    }

    // Sample templates data
    $sample_templates = array(
        array(
            'title' => 'Tech Startup Business Plan',
            'description' => 'Comprehensive business plan template for technology startups in Africa',
            'prompt_content' => 'Create a detailed business plan for a technology startup in {country}. Include executive summary, market analysis, product development strategy, financial projections, and funding requirements. Focus on the African market context and opportunities.',
            'category_id' => $business_plan_cat,
            'tags' => 'technology,startup,business plan,africa',
            'language_code' => 'en',
            'country_code' => '',
            'sector' => 'Technology',
            'usage_count' => 45,
            'rating_average' => 4.8,
            'rating_count' => 12
        ),
        array(
            'title' => 'Agricultural Business Plan',
            'description' => 'Business plan template for agricultural ventures and agribusiness',
            'prompt_content' => 'Develop a comprehensive business plan for an agricultural business in {country}. Include crop selection, market analysis, supply chain strategy, seasonal planning, and financial projections. Consider local climate and market conditions.',
            'category_id' => $business_plan_cat,
            'tags' => 'agriculture,farming,agribusiness,rural',
            'language_code' => 'en',
            'country_code' => '',
            'sector' => 'Agriculture',
            'usage_count' => 38,
            'rating_average' => 4.6,
            'rating_count' => 9
        ),
        array(
            'title' => 'Digital Marketing Strategy',
            'description' => 'Comprehensive digital marketing strategy for African businesses',
            'prompt_content' => 'Create a digital marketing strategy for a business in {country}. Include social media marketing, content strategy, SEO, online advertising, and mobile marketing. Focus on platforms popular in Africa and local consumer behavior.',
            'category_id' => $marketing_cat,
            'tags' => 'digital marketing,social media,online,strategy',
            'language_code' => 'en',
            'country_code' => '',
            'sector' => 'Marketing',
            'usage_count' => 52,
            'rating_average' => 4.7,
            'rating_count' => 15
        ),
        array(
            'title' => 'Small Business Financial Forecast',
            'description' => 'Financial forecasting template for small and medium enterprises',
            'prompt_content' => 'Prepare a 3-year financial forecast for a small business in {country}. Include revenue projections, expense planning, cash flow analysis, break-even analysis, and funding requirements. Consider local economic conditions and currency.',
            'category_id' => $financial_cat,
            'tags' => 'financial,forecast,planning,SME',
            'language_code' => 'en',
            'country_code' => '',
            'sector' => 'Finance',
            'usage_count' => 29,
            'rating_average' => 4.5,
            'rating_count' => 8
        ),
        array(
            'title' => 'E-commerce Business Plan',
            'description' => 'Business plan template for online retail and e-commerce ventures',
            'prompt_content' => 'Create a business plan for an e-commerce business in {country}. Include market analysis, platform selection, logistics strategy, payment solutions, customer acquisition, and growth projections. Focus on African e-commerce landscape.',
            'category_id' => $business_plan_cat,
            'tags' => 'ecommerce,online retail,digital business',
            'language_code' => 'en',
            'country_code' => '',
            'sector' => 'E-commerce',
            'usage_count' => 41,
            'rating_average' => 4.9,
            'rating_count' => 11
        )
    );

    // Insert sample templates
    foreach ($sample_templates as $template) {
        $wpdb->insert($templates_table, array_merge($template, array(
            'user_id' => 0, // System templates
            'is_public' => 1,
            'status' => 'active',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        )));
    }
}

/**
 * Enqueue scripts and styles
 */
function chatgabi_scripts() {
    // Theme stylesheet
    wp_enqueue_style(
        'chatgabi-style',
        CHATGABI_THEME_URL . '/style.css',
        array(),
        CHATGABI_VERSION
    );

    // Paystack integration
    wp_enqueue_script(
        'paystack-inline',
        'https://js.paystack.co/v1/inline.js',
        array(),
        null,
        true
    );

    wp_enqueue_script(
        'chatgabi-payments',
        CHATGABI_THEME_URL . '/assets/js/payments.js',
        array('paystack-inline', 'wp-api-fetch', 'jquery'), // Added 'jquery' dependency
        CHATGABI_VERSION,
        true
    );

    // ChatGABI Dashboard script for tab functionality
    wp_enqueue_script(
        'chatgabi-dashboard',
        CHATGABI_THEME_URL . '/assets/js/chatgabi-dashboard.js',
        array('jquery'), // jQuery as a dependency for simplicity, can be vanilla JS
        CHATGABI_VERSION,
        true
    );

    wp_localize_script('chatgabi-payments', 'paystackConfig', array(
        'publicKey' => defined('CHATGABI_PAYSTACK_PUBLIC_KEY') ? CHATGABI_PAYSTACK_PUBLIC_KEY : get_option('chatgabi_paystack_public_key', ''),
        'restUrl' => rest_url('chatgabi/v1/'),
        'nonce' => wp_create_nonce('chatgabi_payment_nonce'),
    ));

    // Enqueue user preferences assets on preferences page
    if (is_page() && has_shortcode(get_post()->post_content, 'chatgabi_user_preferences')) {
        wp_enqueue_style(
            'chatgabi-preferences',
            CHATGABI_THEME_URL . '/assets/css/user-preferences.css',
            array(),
            CHATGABI_VERSION
        );

        wp_enqueue_script(
            'chatgabi-preferences',
            CHATGABI_THEME_URL . '/assets/js/user-preferences.js',
            array('jquery'),
            CHATGABI_VERSION,
            true
        );

        wp_localize_script('chatgabi-preferences', 'chatgabiPrefs', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('chatgabi_preferences_nonce'),
            'sectorsNonce' => wp_create_nonce('chatgabi_get_sectors'),
            'strings' => array(
                'selectSector' => __('Select Sector...', 'chatgabi'),
                'loadingSectors' => __('Loading sectors...', 'chatgabi'),
                'noSectorsFound' => __('No sectors found', 'chatgabi'),
                'errorLoadingSectors' => __('Error loading sectors', 'chatgabi'),
                'preferencesSaved' => __('Preferences saved successfully!', 'chatgabi'),
                'saveError' => __('Failed to save preferences. Please try again.', 'chatgabi'),
                'networkError' => __('Network error. Please check your connection.', 'chatgabi'),
                'confirmReset' => __('Are you sure you want to reset all preferences to defaults?', 'chatgabi'),
                'preferencesReset' => __('Preferences reset to defaults', 'chatgabi')
            )
        ));
    }

    // Enqueue credit feedback assets on pages with chat functionality
    if (is_page() || has_shortcode(get_post()->post_content ?? '', 'businesscraft_ai_dashboard')) {
        wp_enqueue_style(
            'chatgabi-credit-feedback',
            CHATGABI_THEME_URL . '/assets/css/credit-feedback.css',
            array(),
            CHATGABI_VERSION
        );

        wp_enqueue_script(
            'chatgabi-credit-feedback',
            CHATGABI_THEME_URL . '/assets/js/credit-feedback.js',
            array('jquery'),
            CHATGABI_VERSION,
            true
        );

        wp_localize_script('chatgabi-credit-feedback', 'chatgabiCreditConfig', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('chatgabi_credit_nonce'),
            'tokenNonce' => wp_create_nonce('chatgabi_token_nonce'),
            'strings' => array(
                'sufficientCredits' => __('Sufficient credits', 'chatgabi'),
                'insufficientCredits' => __('Insufficient credits', 'chatgabi'),
                'insufficientCreditsConfirm' => __('This operation requires {credits} credits but you only have {shortfall} credits. Do you want to continue?', 'chatgabi'),
                'lowCreditMessage' => __('You need {shortfall} more credits for this operation', 'chatgabi'),
                'estimationError' => __('Failed to estimate token usage', 'chatgabi'),
                'networkError' => __('Network error occurred', 'chatgabi')
            )
        ));
    }

    // Enqueue prompt templates assets on templates page or dashboard
    if ((is_page() && (has_shortcode(get_post()->post_content ?? '', 'chatgabi_prompt_templates') ||
                      strpos(get_post()->post_content ?? '', 'prompt-templates-manager') !== false)) ||
        is_page_template('page-dashboard.php') || is_page('dashboard') || is_page('templates')) {

        // Chart.js for enhanced preview charts
        wp_enqueue_script(
            'chart-js',
            'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
            array(),
            '3.9.1',
            true
        );

        // Enhanced Template Preview System
        wp_enqueue_style(
            'chatgabi-template-preview-enhanced',
            CHATGABI_THEME_URL . '/assets/css/template-preview-enhanced.css',
            array(),
            CHATGABI_VERSION
        );

        wp_enqueue_style(
            'chatgabi-ai-template-widgets',
            CHATGABI_THEME_URL . '/assets/css/ai-template-widgets.css',
            array(),
            CHATGABI_VERSION
        );

        wp_enqueue_script(
            'chatgabi-template-preview-enhanced',
            CHATGABI_THEME_URL . '/assets/js/template-preview-enhanced.js',
            array('jquery', 'chart-js'),
            CHATGABI_VERSION,
            true
        );

        // Original template styles (for backward compatibility)
        wp_enqueue_style(
            'chatgabi-prompt-templates',
            CHATGABI_THEME_URL . '/assets/css/prompt-templates.css',
            array(),
            CHATGABI_VERSION
        );

        wp_enqueue_script(
            'chatgabi-prompt-templates',
            CHATGABI_THEME_URL . '/assets/js/prompt-templates.js',
            array('jquery', 'chatgabi-template-preview-enhanced'),
            CHATGABI_VERSION,
            true
        );

        // Get user context for enhanced preview
        $user_id = get_current_user_id();
        $user_country = get_user_meta($user_id, 'businesscraft_ai_country', true) ?: 'GH';
        $user_industry = get_user_meta($user_id, 'businesscraft_ai_industry', true) ?: 'general';
        $user_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;

        wp_localize_script('chatgabi-prompt-templates', 'chatgabiTemplatesConfig', array(
            'restUrl' => rest_url('chatgabi/v1/'),
            'restNonce' => wp_create_nonce('wp_rest'),
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('chatgabi_templates_nonce'),
            'categories' => function_exists('chatgabi_get_template_categories') ? chatgabi_get_template_categories() : array(),
            'currentUserId' => get_current_user_id(),
            'isAdmin' => current_user_can('manage_options'),
            'userCredits' => floatval($user_credits),
            'userCountry' => $user_country,
            'userIndustry' => $user_industry,
            'templateGeneratorUrl' => home_url('/dashboard/?tab=chat'),
            'enhancedPreviewEnabled' => true,
            'strings' => array(
                'confirmDelete' => __('Are you sure you want to delete this template?', 'chatgabi'),
                'templateDeleted' => __('Template deleted successfully', 'chatgabi'),
                'templateSaved' => __('Template saved successfully', 'chatgabi'),
                'templateUsed' => __('Template loaded for use', 'chatgabi'),
                'loadError' => __('Failed to load templates', 'chatgabi'),
                'saveError' => __('Failed to save template', 'chatgabi'),
                'deleteError' => __('Failed to delete template', 'chatgabi'),
                'networkError' => __('Network error occurred', 'chatgabi'),
                'loading' => __('Loading...', 'chatgabi'),
                'noTemplates' => __('No templates found.', 'chatgabi'),
                'searchPlaceholder' => __('Search templates...', 'chatgabi'),
                'previewLoading' => __('Loading enhanced preview...', 'chatgabi'),
                'previewError' => __('Failed to load preview', 'chatgabi'),
                'aiEnhancing' => __('AI is enhancing your template...', 'chatgabi'),
                'insufficientCredits' => __('Insufficient credits for AI enhancement', 'chatgabi'),
            )
        ));
    }

    // Enqueue onboarding assets on onboarding page
    if (is_page('onboarding') || (isset($_GET['step']) && !empty($_GET['step']))) {
        wp_enqueue_style(
            'chatgabi-onboarding',
            CHATGABI_THEME_URL . '/assets/css/onboarding.css',
            array(),
            CHATGABI_VERSION
        );

        wp_enqueue_script(
            'chatgabi-onboarding',
            CHATGABI_THEME_URL . '/assets/js/onboarding.js',
            array('jquery'),
            CHATGABI_VERSION,
            true
        );

        wp_localize_script('chatgabi-onboarding', 'chatgabiOnboardingConfig', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('chatgabi_onboarding_nonce'),
            'dashboardUrl' => home_url('/dashboard/'),
            'strings' => array(
                'stepSaved' => __('Step saved successfully', 'chatgabi'),
                'onboardingComplete' => __('Onboarding completed successfully', 'chatgabi'),
                'saveError' => __('Failed to save step data', 'chatgabi'),
                'completeError' => __('Failed to complete onboarding', 'chatgabi'),
                'networkError' => __('Network error occurred', 'chatgabi'),
                'validationError' => __('Please fill in all required fields', 'chatgabi')
            )
        ));
    }

    // Enqueue feedback assets on pages with chat functionality or dashboard
    if (is_page() || is_page_template('page-dashboard.php') || is_page('dashboard') || is_front_page()) {
        wp_enqueue_script(
            'chatgabi-feedback',
            CHATGABI_THEME_URL . '/assets/js/feedback.js',
            array('jquery'),
            CHATGABI_VERSION,
            true
        );

        wp_localize_script('chatgabi-feedback', 'chatgabi_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('chatgabi_feedback_nonce'),
            'strings' => array(
                'loading' => __('Loading...', 'businesscraft-ai'),
                'error' => __('An error occurred', 'businesscraft-ai'),
                'success' => __('Thank you for your feedback!', 'businesscraft-ai'),
                'networkError' => __('Network error occurred', 'businesscraft-ai')
            )
        ));
    }

    // Enqueue response streaming assets on pages with chat functionality
    if (is_page() || is_page_template('page-dashboard.php') || is_page('dashboard') || is_front_page()) {
        wp_enqueue_script(
            'chatgabi-response-streaming',
            CHATGABI_THEME_URL . '/assets/js/response-streaming.js',
            array('jquery'),
            CHATGABI_VERSION,
            true
        );

        wp_localize_script('chatgabi-response-streaming', 'businesscraftAI', array(
            'streamUrl' => rest_url('bcai/v1/stream/chat'),
            'restUrl' => rest_url('bcai/v1/'),
            'nonce' => wp_create_nonce('wp_rest'),
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'streamingEnabled' => defined('CHATGABI_ENABLE_RESPONSE_STREAMING') ? CHATGABI_ENABLE_RESPONSE_STREAMING : true
        ));

        // Enhancement Features Scripts
        wp_enqueue_script(
            'chatgabi-feedback-loops',
            CHATGABI_THEME_URL . '/assets/js/feedback-loops.js',
            array('jquery'),
            CHATGABI_VERSION,
            true
        );

        wp_enqueue_script(
            'chatgabi-personalization',
            CHATGABI_THEME_URL . '/assets/js/personalization.js',
            array('jquery'),
            CHATGABI_VERSION,
            true
        );

        wp_enqueue_script(
            'chatgabi-offline-queue',
            CHATGABI_THEME_URL . '/assets/js/offline-queue.js',
            array('jquery'),
            CHATGABI_VERSION,
            true
        );

        wp_enqueue_script(
            'chatgabi-local-storage',
            CHATGABI_THEME_URL . '/assets/js/local-storage-manager.js',
            array('jquery'),
            CHATGABI_VERSION,
            true
        );

        wp_enqueue_script(
            'chatgabi-advanced-analytics',
            CHATGABI_THEME_URL . '/assets/js/advanced-analytics.js',
            array('jquery', 'chart-js'),
            CHATGABI_VERSION,
            true
        );

        // Localize enhancement scripts
        wp_localize_script('chatgabi-feedback-loops', 'chatgabiFeedback', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('chatgabi_feedback_nonce'),
            'showFeedbackInterface' => true
        ));

        wp_localize_script('chatgabi-personalization', 'chatgabiPersonalization', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'restUrl' => rest_url('chatgabi/v1/'),
            'nonce' => wp_create_nonce('chatgabi_context_nonce')
        ));

        wp_localize_script('chatgabi-advanced-analytics', 'chatgabiAnalytics', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'restUrl' => rest_url('chatgabi/v1/'),
            'nonce' => wp_create_nonce('chatgabi_analytics_nonce')
        ));
    }

    // Phase 3: Feature Completion assets for dashboard
    if (is_page_template('page-dashboard.php') || is_page('dashboard')) {
        // Enqueue Chart.js for analytics
        wp_enqueue_script(
            'chart-js',
            'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
            array(),
            '3.9.1',
            true
        );

        // Enqueue WordPress hooks for frontend
        wp_enqueue_script('wp-hooks');

        // Enqueue Phase 3 styles
        wp_enqueue_style(
            'chatgabi-dashboard-phase3',
            CHATGABI_THEME_URL . '/assets/css/dashboard-phase3.css',
            array(),
            CHATGABI_VERSION
        );

        // Enqueue responsive accessibility framework
        wp_enqueue_style(
            'chatgabi-responsive-accessibility',
            CHATGABI_THEME_URL . '/assets/css/responsive-accessibility.css',
            array(),
            CHATGABI_VERSION
        );

        // Enqueue component-based architecture
        wp_enqueue_script(
            'chatgabi-base-component',
            CHATGABI_THEME_URL . '/assets/js/components/base-component.js',
            array('wp-hooks'),
            CHATGABI_VERSION,
            true
        );

        wp_enqueue_script(
            'chatgabi-component-manager',
            CHATGABI_THEME_URL . '/assets/js/components/component-manager.js',
            array('chatgabi-base-component'),
            CHATGABI_VERSION,
            true
        );

        wp_enqueue_script(
            'chatgabi-analytics-dashboard',
            CHATGABI_THEME_URL . '/assets/js/components/analytics-dashboard.js',
            array('chatgabi-component-manager', 'chart-js'),
            CHATGABI_VERSION,
            true
        );

        wp_enqueue_script(
            'chatgabi-notification-center',
            CHATGABI_THEME_URL . '/assets/js/components/notification-center.js',
            array('chatgabi-component-manager'),
            CHATGABI_VERSION,
            true
        );

        wp_enqueue_script(
            'chatgabi-template-enhancer',
            CHATGABI_THEME_URL . '/assets/js/components/template-enhancer.js',
            array('chatgabi-component-manager'),
            CHATGABI_VERSION,
            true
        );

        // Enqueue Phase 3 scripts (now with component integration)
        wp_enqueue_script(
            'chatgabi-dashboard-phase3',
            CHATGABI_THEME_URL . '/assets/js/dashboard-phase3.js',
            array('chatgabi-analytics-dashboard', 'chatgabi-notification-center', 'chatgabi-template-enhancer'),
            CHATGABI_VERSION,
            true
        );
    }

    // Add shortcode page styles
    wp_add_inline_style('chatgabi-style', '
        .chatgabi-notice, .chatgabi-error {
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: center;
        }
        .chatgabi-notice {
            background: #e7f3ff;
            border: 1px solid #007cba;
            color: #007cba;
        }
        .chatgabi-error {
            background: #ffe7e7;
            border: 1px solid #dc3232;
            color: #dc3232;
        }
        .chatgabi-opportunities-page {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .chatgabi-opportunities-page h1 {
            color: #007cba;
            margin-bottom: 10px;
        }
        .chatgabi-opportunities-page > p {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
    ');

    // Add location debug info for development
    if (defined('WP_DEBUG') && WP_DEBUG) {
        // Ensure location functions are available
        chatgabi_ensure_location_functions();

        wp_localize_script('chatgabi-payments', 'locationDebug', array(
            'enabled' => true,
            'userIP' => function_exists('businesscraft_ai_get_user_ip') ? businesscraft_ai_get_user_ip() : 'unknown',
            'detectedCountry' => function_exists('businesscraft_ai_get_user_country') ? businesscraft_ai_get_user_country() : 'GH',
            'userCurrency' => function_exists('businesscraft_ai_get_user_currency') ? businesscraft_ai_get_user_currency() : array('code' => 'GHS', 'symbol' => '₵'),
        ));
    }
}
add_action('wp_enqueue_scripts', 'chatgabi_scripts');

/**
 * Ensure location functions are available when needed
 */
function chatgabi_ensure_location_functions() {
    // Load paystack integration if not already loaded (contains location functions)
    if (!function_exists('businesscraft_ai_get_user_ip')) {
        $paystack_file = CHATGABI_THEME_DIR . '/inc/paystack-integration.php';
        if (file_exists($paystack_file)) {
            require_once $paystack_file;
        }
    }

    // Provide fallback functions if still not available
    if (!function_exists('businesscraft_ai_get_user_ip')) {
        function businesscraft_ai_get_user_ip() {
            // Simple IP detection fallback
            $ip_headers = array(
                'HTTP_CF_CONNECTING_IP',     // Cloudflare
                'HTTP_CLIENT_IP',            // Proxy
                'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
                'HTTP_X_FORWARDED',          // Proxy
                'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
                'HTTP_FORWARDED_FOR',        // Proxy
                'HTTP_FORWARDED',            // Proxy
                'REMOTE_ADDR'                // Standard
            );

            foreach ($ip_headers as $header) {
                if (!empty($_SERVER[$header])) {
                    $ip = $_SERVER[$header];
                    // Handle comma-separated IPs (take first one)
                    if (strpos($ip, ',') !== false) {
                        $ip = trim(explode(',', $ip)[0]);
                    }
                    // Validate IP
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                        return $ip;
                    }
                }
            }

            return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        }
    }

    if (!function_exists('businesscraft_ai_get_user_country')) {
        function businesscraft_ai_get_user_country() {
            // Simple country detection fallback
            if (isset($_SESSION['businesscraft_ai_country'])) {
                return $_SESSION['businesscraft_ai_country'];
            }

            // Try to get from user meta if logged in
            $user_id = get_current_user_id();
            if ($user_id) {
                $stored_country = get_user_meta($user_id, 'businesscraft_ai_country', true);
                if ($stored_country) {
                    return $stored_country;
                }
            }

            // Default to Ghana
            return 'GH';
        }
    }

    if (!function_exists('businesscraft_ai_get_user_currency')) {
        function businesscraft_ai_get_user_currency() {
            // Simple currency detection fallback
            $country = function_exists('businesscraft_ai_get_user_country') ? businesscraft_ai_get_user_country() : 'GH';

            $currencies = array(
                'GH' => array('code' => 'GHS', 'symbol' => '₵'),
                'KE' => array('code' => 'KES', 'symbol' => 'KSh'),
                'NG' => array('code' => 'NGN', 'symbol' => '₦'),
                'ZA' => array('code' => 'ZAR', 'symbol' => 'R'),
            );

            return $currencies[$country] ?? $currencies['GH'];
        }
    }
}

/**
 * Enqueue editor-specific scripts and styles for Gutenberg blocks.
 */
function chatgabi_enqueue_editor_assets() {
    // Enqueue editor style
    wp_enqueue_style(
        'chatgabi-editor-style',
        CHATGABI_THEME_URL . '/assets/css/editor-style.css',
        array('wp-edit-blocks'),
        CHATGABI_VERSION
    );
}
add_action('enqueue_block_editor_assets', 'chatgabi_enqueue_editor_assets');

/**
 * Enqueue chat block script and localize data for frontend.
 */
function chatgabi_enqueue_chat_block_frontend_scripts() {
    wp_enqueue_script(
        'chatgabi-chat-block',
        CHATGABI_THEME_URL . '/assets/js/chat-block.js',
        array('wp-api-fetch'), // Only wp-api-fetch needed for frontend
        CHATGABI_VERSION,
        true
    );

    wp_localize_script('chatgabi-chat-block', 'chatgabiAI', array(
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'restUrl' => rest_url('chatgabi/v1/'),
        'themeUrl' => CHATGABI_THEME_URL,
        'nonce' => wp_create_nonce('wp_rest'), // Use a general REST API nonce for frontend
        'strings' => array(
            'loading' => __('Processing...', 'chatgabi'),
            'error' => __('An error occurred. Please try again.', 'chatgabi'),
            'insufficientCredits' => __('Insufficient credits. Please purchase more credits.', 'chatgabi'),
            'networkError' => __('Network error. Please check your connection.', 'chatgabi'),
        ),
        'languages' => array(
            'en' => __('English', 'chatgabi'),
            'tw' => __('Twi', 'chatgabi'),
            'sw' => __('Swahili', 'chatgabi'),
            'yo' => __('Yoruba', 'chatgabi'),
            'zu' => __('Zulu', 'chatgabi'),
        ),
        'prompts' => array(
            'en' => array(
                __('Create a business plan for a tech startup', 'chatgabi'),
                __('Write a marketing strategy for small business', 'chatgabi'),
                __('Generate a financial forecast for next year', 'chatgabi'),
                __('Create an SOP for customer service', 'chatgabi'),
            ),
            'tw' => array(
                __('Yɛ adwumayɛ nhyehyɛe ma teknoloji adwuma', 'chatgabi'),
                __('Kyerɛw aguadi nhyehyɛe ma adwuma ketewa', 'chatgabi'),
            ),
            'sw' => array(
                __('Tengeneza mpango wa biashara kwa kampuni ya teknolojia', 'chatgabi'),
                __('Andika mkakati wa uuzaji kwa biashara ndogo', 'chatgabi'),
            ),
            'yo' => array(
                __('Ṣe eto iṣowo fun ile-iṣẹ imọ-ẹrọ', 'chatgabi'),
                __('Kọ ilana titaja fun iṣowo kekere', 'chatgabi'),
            ),
            'zu' => array(
                __('Dala uhlelo lwebhizinisi lwenkampani yezobuchwepheshe', 'chatgabi'),
                __('Bhala isu lokuthengisa lebhizinisi elincane', 'chatgabi'),
            ),
        ),
    ));
}
add_action('wp_enqueue_scripts', 'chatgabi_enqueue_chat_block_frontend_scripts');


/**
 * Register Gutenberg blocks
 */
function businesscraft_ai_register_blocks() {
    // Register chat block
    register_block_type('businesscraft-ai/chat-block', array(
        'render_callback' => 'businesscraft_ai_render_chat_block',
        'attributes' => array(
            'showHistory' => array(
                'type' => 'boolean',
                'default' => true,
            ),
            'showExamples' => array(
                'type' => 'boolean',
                'default' => true,
            ),
            'maxHistory' => array(
                'type' => 'number',
                'default' => 10,
            ),
        ),
    ));
}
add_action('init', 'businesscraft_ai_register_blocks');

/**
 * Register shortcodes for dashboard pages
 */
function businesscraft_ai_register_shortcodes() {
    add_shortcode('businesscraft_ai_dashboard', 'businesscraft_ai_dashboard_shortcode');
    add_shortcode('businesscraft_ai_opportunities', 'businesscraft_ai_opportunities_shortcode');
    add_shortcode('chatgabi_user_preferences', 'chatgabi_user_preferences_shortcode');
    add_shortcode('chatgabi_prompt_templates', 'chatgabi_prompt_templates_shortcode');
    add_shortcode('chatgabi_onboarding', 'chatgabi_onboarding_shortcode');
}
add_action('init', 'businesscraft_ai_register_shortcodes');

// Initialize ChatGABI template categories on theme activation and init
add_action('after_switch_theme', 'chatgabi_init_template_categories');
add_action('init', 'chatgabi_init_template_categories'); // Also run on init for existing installations

// Enhanced Preview System Integration
require_once get_template_directory() . '/inc/database-migration-enhanced-preview.php';
require_once get_template_directory() . '/inc/template-preview-system.php';
require_once get_template_directory() . '/inc/ai-template-widgets.php';
require_once get_template_directory() . '/inc/enhanced-template-forms.php';

// REST API Integration
require_once get_template_directory() . '/inc/rest-api.php';

// Execute database migration for enhanced preview system
add_action('after_switch_theme', 'chatgabi_check_enhanced_preview_migration');
add_action('admin_init', 'chatgabi_check_enhanced_preview_migration');

/**
 * Check and execute enhanced preview system migration
 */
function chatgabi_check_enhanced_preview_migration() {
    if (chatgabi_needs_enhanced_preview_migration()) {
        $result = chatgabi_execute_enhanced_preview_migration();

        if (!$result['success']) {
            // Log error for admin notice
            set_transient('chatgabi_migration_error', $result['message'], 300);
        }
    }
}

// Add templates page to WordPress admin menu
add_action('admin_menu', 'chatgabi_add_templates_admin_menu');

/**
 * Add ChatGABI Templates to admin menu
 */
function chatgabi_add_templates_admin_menu() {
    add_menu_page(
        __('ChatGABI Templates', 'chatgabi'),
        __('Templates', 'chatgabi'),
        'manage_options',
        'chatgabi-templates',
        'chatgabi_templates_admin_page',
        'dashicons-format-aside',
        30
    );
}

/**
 * Admin page for template management
 */
function chatgabi_templates_admin_page() {
    global $wpdb;

    // Get template statistics
    $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
    $categories_table = $wpdb->prefix . 'chatgabi_template_categories';

    $total_templates = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table");
    $public_templates = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE is_public = 1");
    $total_categories = $wpdb->get_var("SELECT COUNT(*) FROM $categories_table");

    // Get recent templates
    $recent_templates = $wpdb->get_results("
        SELECT t.*, c.name as category_name
        FROM $templates_table t
        LEFT JOIN $categories_table c ON t.category_id = c.id
        ORDER BY t.created_at DESC
        LIMIT 10
    ");

    // Get categories with template counts
    $categories = $wpdb->get_results("
        SELECT c.*, COUNT(t.id) as template_count
        FROM $categories_table c
        LEFT JOIN $templates_table t ON c.id = t.category_id
        GROUP BY c.id
        ORDER BY c.sort_order ASC
    ");

    ?>
    <div class="wrap">
        <h1><?php _e('ChatGABI Templates Management', 'chatgabi'); ?></h1>
        <p><?php _e('Manage business templates for the ChatGABI system.', 'chatgabi'); ?></p>

        <!-- Quick Stats -->
        <div class="chatgabi-admin-stats" style="display: flex; gap: 20px; margin: 20px 0;">
            <div class="stat-card" style="background: #f1f1f1; padding: 15px; border-radius: 5px; flex: 1;">
                <h3 style="margin: 0; color: #007cba;"><?php echo $total_templates; ?></h3>
                <p style="margin: 5px 0 0 0;"><?php _e('Total Templates', 'chatgabi'); ?></p>
            </div>
            <div class="stat-card" style="background: #f1f1f1; padding: 15px; border-radius: 5px; flex: 1;">
                <h3 style="margin: 0; color: #28a745;"><?php echo $public_templates; ?></h3>
                <p style="margin: 5px 0 0 0;"><?php _e('Public Templates', 'chatgabi'); ?></p>
            </div>
            <div class="stat-card" style="background: #f1f1f1; padding: 15px; border-radius: 5px; flex: 1;">
                <h3 style="margin: 0; color: #ffc107;"><?php echo $total_categories; ?></h3>
                <p style="margin: 5px 0 0 0;"><?php _e('Categories', 'chatgabi'); ?></p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div style="margin: 20px 0;">
            <a href="<?php echo home_url('/templates'); ?>" class="button button-primary">
                <?php _e('View Templates Interface', 'chatgabi'); ?>
            </a>
            <button type="button" class="button" onclick="location.reload()">
                <?php _e('Refresh Data', 'chatgabi'); ?>
            </button>
        </div>

        <!-- Categories Overview -->
        <h2><?php _e('Template Categories', 'chatgabi'); ?></h2>
        <?php if (!empty($categories)): ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Name', 'chatgabi'); ?></th>
                        <th><?php _e('Slug', 'chatgabi'); ?></th>
                        <th><?php _e('Templates', 'chatgabi'); ?></th>
                        <th><?php _e('Status', 'chatgabi'); ?></th>
                        <th><?php _e('Sort Order', 'chatgabi'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($categories as $category): ?>
                        <tr>
                            <td>
                                <strong><?php echo esc_html($category->name); ?></strong>
                                <?php if ($category->icon): ?>
                                    <span style="margin-left: 10px;"><?php echo esc_html($category->icon); ?></span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo esc_html($category->slug); ?></td>
                            <td><?php echo intval($category->template_count); ?></td>
                            <td>
                                <?php
                                $status = isset($category->status) ? $category->status : 'active';
                                $status_color = $status === 'active' ? '#28a745' : '#dc3545';
                                ?>
                                <span style="color: <?php echo $status_color; ?>;">
                                    <?php echo esc_html(ucfirst($status)); ?>
                                </span>
                            </td>
                            <td><?php echo intval($category->sort_order); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p><?php _e('No template categories found.', 'chatgabi'); ?></p>
        <?php endif; ?>

        <!-- Recent Templates -->
        <h2><?php _e('Recent Templates', 'chatgabi'); ?></h2>
        <?php if (!empty($recent_templates)): ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Title', 'chatgabi'); ?></th>
                        <th><?php _e('Category', 'chatgabi'); ?></th>
                        <th><?php _e('User', 'chatgabi'); ?></th>
                        <th><?php _e('Public', 'chatgabi'); ?></th>
                        <th><?php _e('Created', 'chatgabi'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_templates as $template): ?>
                        <tr>
                            <td>
                                <strong><?php echo esc_html($template->title); ?></strong>
                                <?php if ($template->description): ?>
                                    <br><small style="color: #666;"><?php echo esc_html(wp_trim_words($template->description, 10)); ?></small>
                                <?php endif; ?>
                                <div class="row-actions" style="margin-top: 5px;">
                                    <span class="edit">
                                        <a href="#" onclick="editTemplate(<?php echo $template->id; ?>)" title="<?php _e('Edit Template', 'chatgabi'); ?>">
                                            <?php _e('Edit', 'chatgabi'); ?>
                                        </a> |
                                    </span>
                                    <span class="duplicate">
                                        <a href="#" onclick="duplicateTemplate(<?php echo $template->id; ?>)" title="<?php _e('Duplicate Template', 'chatgabi'); ?>">
                                            <?php _e('Duplicate', 'chatgabi'); ?>
                                        </a> |
                                    </span>
                                    <span class="view">
                                        <a href="#" onclick="viewTemplate(<?php echo $template->id; ?>)" title="<?php _e('View Template', 'chatgabi'); ?>">
                                            <?php _e('View', 'chatgabi'); ?>
                                        </a>
                                        <?php if ($template->user_id != 0): ?>
                                        |
                                        <span class="delete">
                                            <a href="#" onclick="deleteTemplate(<?php echo $template->id; ?>)" title="<?php _e('Delete Template', 'chatgabi'); ?>" style="color: #dc3545;">
                                                <?php _e('Delete', 'chatgabi'); ?>
                                            </a>
                                        </span>
                                        <?php endif; ?>
                                    </span>
                                </div>
                            </td>
                            <td><?php echo esc_html($template->category_name ?: 'Uncategorized'); ?></td>
                            <td>
                                <?php
                                if ($template->user_id == 0) {
                                    echo '<em>' . __('System', 'chatgabi') . '</em>';
                                } else {
                                    $user = get_user_by('id', $template->user_id);
                                    echo $user ? esc_html($user->display_name) : __('Unknown', 'chatgabi');
                                }
                                ?>
                            </td>
                            <td>
                                <?php if ($template->is_public): ?>
                                    <span style="color: #28a745;">✓ <?php _e('Yes', 'chatgabi'); ?></span>
                                <?php else: ?>
                                    <span style="color: #dc3545;">✗ <?php _e('No', 'chatgabi'); ?></span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo esc_html(date('Y-m-d H:i', strtotime($template->created_at))); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p><?php _e('No templates found.', 'chatgabi'); ?></p>
        <?php endif; ?>
    </div>

    <!-- Template Management JavaScript -->
    <script type="text/javascript">
    function editTemplate(templateId) {
        // Open edit modal or redirect to edit page
        var editUrl = '<?php echo admin_url('admin.php?page=chatgabi-templates&action=edit&template_id='); ?>' + templateId;
        window.location.href = editUrl;
    }

    function duplicateTemplate(templateId) {
        if (confirm('<?php _e('Are you sure you want to duplicate this template?', 'chatgabi'); ?>')) {
            jQuery.post(ajaxurl, {
                action: 'chatgabi_duplicate_template',
                template_id: templateId,
                nonce: '<?php echo wp_create_nonce('chatgabi_admin_nonce'); ?>'
            }, function(response) {
                if (response.success) {
                    alert('<?php _e('Template duplicated successfully!', 'chatgabi'); ?>');
                    location.reload();
                } else {
                    alert('<?php _e('Error duplicating template: ', 'chatgabi'); ?>' + response.data);
                }
            });
        }
    }

    function viewTemplate(templateId) {
        // Open view modal or redirect to view page
        var viewUrl = '<?php echo admin_url('admin.php?page=chatgabi-templates&action=view&template_id='); ?>' + templateId;
        window.open(viewUrl, '_blank');
    }

    function deleteTemplate(templateId) {
        if (confirm('<?php _e('Are you sure you want to delete this template? This action cannot be undone.', 'chatgabi'); ?>')) {
            jQuery.post(ajaxurl, {
                action: 'chatgabi_delete_template',
                template_id: templateId,
                nonce: '<?php echo wp_create_nonce('chatgabi_admin_nonce'); ?>'
            }, function(response) {
                if (response.success) {
                    alert('<?php _e('Template deleted successfully!', 'chatgabi'); ?>');
                    location.reload();
                } else {
                    alert('<?php _e('Error deleting template: ', 'chatgabi'); ?>' + response.data);
                }
            });
        }
    }
    </script>

    <?php
}

/**
 * AJAX handler for duplicating templates
 */
function chatgabi_ajax_duplicate_template() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_admin_nonce')) {
        wp_die('Security check failed');
    }

    // Check user permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    $template_id = intval($_POST['template_id']);

    global $wpdb;
    $table = $wpdb->prefix . 'chatgabi_prompt_templates';

    // Get original template
    $template = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table WHERE id = %d", $template_id));

    if (!$template) {
        wp_send_json_error('Template not found');
        return;
    }

    // Create duplicate
    $duplicate_data = array(
        'title' => $template->title . ' (Copy)',
        'description' => $template->description,
        'prompt_content' => $template->prompt_content,
        'category_id' => $template->category_id,
        'user_id' => get_current_user_id(),
        'is_public' => 0, // Duplicates are private by default
        'is_featured' => 0,
        'language' => $template->language,
        'sector' => $template->sector,
        'country' => $template->country,
        'tags' => $template->tags,
        'placeholders' => $template->placeholders,
        'status' => 'active',
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    );

    $result = $wpdb->insert($table, $duplicate_data);

    if ($result) {
        wp_send_json_success('Template duplicated successfully');
    } else {
        wp_send_json_error('Failed to duplicate template');
    }
}
add_action('wp_ajax_chatgabi_duplicate_template', 'chatgabi_ajax_duplicate_template');

/**
 * AJAX handler for deleting templates
 */
function chatgabi_ajax_delete_template() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_admin_nonce')) {
        wp_die('Security check failed');
    }

    // Check user permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    $template_id = intval($_POST['template_id']);

    global $wpdb;
    $table = $wpdb->prefix . 'chatgabi_prompt_templates';

    // Check if template exists and is not a system template
    $template = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table WHERE id = %d", $template_id));

    if (!$template) {
        wp_send_json_error('Template not found');
        return;
    }

    if ($template->user_id == 0) {
        wp_send_json_error('Cannot delete system templates');
        return;
    }

    // Delete template
    $result = $wpdb->delete($table, array('id' => $template_id), array('%d'));

    if ($result) {
        wp_send_json_success('Template deleted successfully');
    } else {
        wp_send_json_error('Failed to delete template');
    }
}
add_action('wp_ajax_chatgabi_delete_template', 'chatgabi_ajax_delete_template');

/**
 * Dashboard shortcode handler
 */
function businesscraft_ai_dashboard_shortcode($atts) {
    // Check if user is logged in
    if (!is_user_logged_in()) {
        return '<div class="businesscraft-ai-notice"><p>Please <a href="' . wp_login_url(get_permalink()) . '">log in</a> to access your dashboard.</p></div>';
    }

    // Start output buffering
    ob_start();

    // Include the dashboard template
    $dashboard_template = get_template_directory() . '/page-dashboard.php';
    if (file_exists($dashboard_template)) {
        // Set up global variables that the template expects
        global $post;
        $original_post = $post;

        // Create a fake post object for the template
        $post = (object) array(
            'ID' => 0,
            'post_title' => 'AI Dashboard',
            'post_content' => '',
            'post_type' => 'page'
        );

        include $dashboard_template;

        // Restore original post
        $post = $original_post;
    } else {
        echo '<div class="businesscraft-ai-error"><p>Dashboard template not found.</p></div>';
    }

    return ob_get_clean();
}

/**
 * Opportunities shortcode handler
 */
function businesscraft_ai_opportunities_shortcode($atts) {
    // Check if user is logged in
    if (!is_user_logged_in()) {
        return '<div class="businesscraft-ai-notice"><p>Please <a href="' . wp_login_url(get_permalink()) . '">log in</a> to access opportunities.</p></div>';
    }

    // Start output buffering
    ob_start();

    // Include the opportunities template part
    $opportunities_template = get_template_directory() . '/template-parts/dashboard-opportunities.php';
    if (file_exists($opportunities_template)) {
        echo '<div class="businesscraft-ai-opportunities-page">';
        echo '<h1>Live Business Opportunities</h1>';
        echo '<p>Discover funding opportunities, grants, and business programs tailored to your sector and location.</p>';
        include $opportunities_template;
        echo '</div>';
    } else {
        echo '<div class="businesscraft-ai-error"><p>Opportunities template not found.</p></div>';
    }

    return ob_get_clean();
}

/**
 * User Preferences shortcode handler
 */
function chatgabi_user_preferences_shortcode($atts) {
    // Check if user is logged in
    if (!is_user_logged_in()) {
        return '<div class="chatgabi-notice"><p>Please <a href="' . wp_login_url(get_permalink()) . '">log in</a> to access your preferences.</p></div>';
    }

    // Start output buffering
    ob_start();

    // Include the preferences template
    $preferences_template = get_template_directory() . '/template-parts/user-preferences.php';
    if (file_exists($preferences_template)) {
        echo '<div class="chatgabi-preferences-page">';
        include $preferences_template;
        echo '</div>';
    } else {
        echo '<div class="chatgabi-error"><p>Preferences template not found.</p></div>';
    }

    return ob_get_clean();
}

/**
 * Prompt Templates shortcode handler
 */
function chatgabi_prompt_templates_shortcode($atts) {
    // Check if user is logged in
    if (!is_user_logged_in()) {
        return '<div class="chatgabi-notice"><p>Please <a href="' . wp_login_url(get_permalink()) . '">log in</a> to access your prompt templates.</p></div>';
    }

    // Start output buffering
    ob_start();

    // Include the templates manager template
    $templates_template = get_template_directory() . '/template-parts/prompt-templates-manager.php';
    if (file_exists($templates_template)) {
        echo '<div class="chatgabi-templates-page">';
        include $templates_template;
        echo '</div>';
    } else {
        echo '<div class="chatgabi-error"><p>Templates manager not found.</p></div>';
    }

    return ob_get_clean();
}

/**
 * Onboarding shortcode handler
 */
function chatgabi_onboarding_shortcode($atts) {
    // Check if user is logged in
    if (!is_user_logged_in()) {
        return '<div class="chatgabi-notice"><p>Please <a href="' . wp_login_url(get_permalink()) . '">log in</a> to access onboarding.</p></div>';
    }

    // Start output buffering
    ob_start();

    // Include the onboarding flow template
    $onboarding_template = get_template_directory() . '/template-parts/onboarding-flow.php';
    if (file_exists($onboarding_template)) {
        echo '<div class="chatgabi-onboarding-page">';
        include $onboarding_template;
        echo '</div>';
    } else {
        echo '<div class="chatgabi-error"><p>Onboarding template not found.</p></div>';
    }

    return ob_get_clean();
}

/**
 * Render chat block
 */
function businesscraft_ai_render_chat_block($attributes) {
    $user_id = get_current_user_id();
    $credits = 0;

    if ($user_id) {
        $credits = get_user_meta($user_id, 'businesscraft_credits', true);
        $credits = $credits ? intval($credits) : 0;
    }

    $show_history = isset($attributes['showHistory']) ? $attributes['showHistory'] : true;
    $show_examples = isset($attributes['showExamples']) ? $attributes['showExamples'] : true;
    $max_history = isset($attributes['maxHistory']) ? intval($attributes['maxHistory']) : 10;

    ob_start();
    ?>
    <div class="businesscraft-ai-chat" data-show-history="<?php echo $show_history ? 'true' : 'false'; ?>" data-show-examples="<?php echo $show_examples ? 'true' : 'false'; ?>" data-max-history="<?php echo $max_history; ?>">
        <div class="chat-header">
            <h3 class="chat-title"><?php _e('BusinessCraft AI Assistant', 'businesscraft-ai'); ?></h3>
            <?php if ($user_id): ?>
                <div class="credit-display">
                    <?php printf(__('Credits: %d', 'businesscraft-ai'), $credits); ?>
                </div>
            <?php else: ?>
                <div class="login-prompt">
                    <a href="<?php echo wp_login_url(get_permalink()); ?>" class="login-link">
                        <?php _e('Login to use AI Assistant', 'businesscraft-ai'); ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <?php if ($user_id):
            $preferred_language = get_user_meta($user_id, 'bcai_preferred_language', true) ?: 'en';
            $show_chat_history = get_user_meta($user_id, 'bcai_show_chat_history', true) !== '0';
        ?>
            <div class="chat-controls">
                <div class="language-selector">
                    <label for="chat-language"><?php _e('Language:', 'businesscraft-ai'); ?></label>
                    <select id="chat-language" name="language">
                        <option value="en" <?php selected($preferred_language, 'en'); ?>><?php _e('English', 'businesscraft-ai'); ?></option>
                        <option value="tw" <?php selected($preferred_language, 'tw'); ?>><?php _e('Twi', 'businesscraft-ai'); ?></option>
                        <option value="sw" <?php selected($preferred_language, 'sw'); ?>><?php _e('Swahili', 'businesscraft-ai'); ?></option>
                        <option value="yo" <?php selected($preferred_language, 'yo'); ?>><?php _e('Yoruba', 'businesscraft-ai'); ?></option>
                        <option value="zu" <?php selected($preferred_language, 'zu'); ?>><?php _e('Zulu', 'businesscraft-ai'); ?></option>
                    </select>
                </div>

                <div class="template-selector">
                    <label for="prompt-templates"><?php _e('Templates:', 'businesscraft-ai'); ?></label>
                    <select id="prompt-templates">
                        <option value=""><?php _e('Select a template...', 'businesscraft-ai'); ?></option>
                    </select>
                    <button type="button" id="save-template-btn" class="save-template-btn" title="<?php _e('Save current message as template', 'businesscraft-ai'); ?>">💾</button>
                </div>

                <div class="preferences-link">
                    <a href="<?php echo home_url('/preferences/'); ?>" class="preferences-btn" title="<?php _e('User Preferences', 'businesscraft-ai'); ?>">⚙️</a>
                </div>
            </div>

            <div class="chat-input-container">
                <!-- Business Templates Quick Access -->
                <div class="chat-templates-access">
                    <a href="<?php echo home_url('/templates'); ?>" class="templates-quick-btn">
                        🚀 <?php _e('Business Templates', 'businesscraft-ai'); ?>
                    </a>
                    <span class="templates-hint"><?php _e('Generate professional business documents', 'businesscraft-ai'); ?></span>
                </div>

                <textarea
                    id="chat-input"
                    class="chat-input"
                    placeholder="<?php _e('Ask me to help with your business needs...', 'businesscraft-ai'); ?>"
                    rows="4"
                ></textarea>
                <button id="chat-submit" class="chat-submit" type="button">
                    <?php _e('Send Message', 'businesscraft-ai'); ?>
                </button>
            </div>

            <?php if ($show_examples): ?>
                <div class="prompt-examples">
                    <h4><?php _e('Example Prompts', 'businesscraft-ai'); ?></h4>
                    <div class="example-prompts" id="example-prompts">
                        <!-- Populated by JavaScript based on selected language -->
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($show_history): ?>
                <div class="chat-history" id="chat-history">
                    <!-- Chat history will be loaded here -->
                </div>
            <?php endif; ?>

            <div id="chat-messages" class="chat-messages">
                <!-- New messages appear here -->
            </div>
        <?php else: ?>
            <div class="login-required">
                <p><?php _e('Please log in to access the BusinessCraft AI Assistant.', 'businesscraft-ai'); ?></p>
                <a href="<?php echo wp_login_url(get_permalink()); ?>" class="button">
                    <?php _e('Login', 'businesscraft-ai'); ?>
                </a>
                <a href="<?php echo wp_registration_url(); ?>" class="button">
                    <?php _e('Register', 'businesscraft-ai'); ?>
                </a>
            </div>
        <?php endif; ?>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Add credits to new users
 */
function businesscraft_ai_add_credits_to_new_user($user_id) {
    add_user_meta($user_id, 'businesscraft_credits', 50, true);
}
add_action('user_register', 'businesscraft_ai_add_credits_to_new_user');

/**
 * Redirect new users to onboarding (DISABLED for testing)
 */
function businesscraft_ai_redirect_to_onboarding() {
    // Temporarily disabled to prevent redirect loops during testing
    // TODO: Re-enable when onboarding page is created
    /*
    if (is_user_logged_in() && !is_admin()) {
        $user_id = get_current_user_id();
        $profile_type = get_user_meta($user_id, 'bcai_profile_type', true);

        // If user hasn't completed onboarding and not already on onboarding page
        if (!$profile_type && !is_page('onboarding') && !wp_doing_ajax()) {
            wp_redirect(home_url('/onboarding/'));
            exit;
        }
    }
    */
}
// add_action('template_redirect', 'businesscraft_ai_redirect_to_onboarding'); // Disabled for testing

/**
 * Set default templates based on profile type
 */
function businesscraft_ai_set_default_templates($user_id, $profile_type, $industry) {
    $templates = array();

    if ($profile_type === 'sme') {
        $templates = array(
            'market_analysis' => array(
                'id' => 'default_market_analysis',
                'name' => 'Market Analysis Template',
                'prompt' => 'Analyze the market for [your business/product] in [your country]. Include market size, competition, opportunities, and challenges specific to the African market.',
                'category' => 'business_intelligence',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            'business_plan' => array(
                'id' => 'default_business_plan',
                'name' => 'Business Plan Template',
                'prompt' => 'Help me create a business plan for [describe your business idea]. Include executive summary, market analysis, financial projections, and implementation strategy for the African market.',
                'category' => 'planning',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            'financial_planning' => array(
                'id' => 'default_financial_planning',
                'name' => 'Financial Planning Template',
                'prompt' => 'Create a financial plan for my [business type] in [country]. Include startup costs, revenue projections, cash flow analysis, and funding requirements.',
                'category' => 'finance',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            'competitive_analysis' => array(
                'id' => 'default_competitive_analysis',
                'name' => 'Competitive Analysis Template',
                'prompt' => 'Analyze the competition for [your business/product] in [your country]. Identify key competitors, their strengths/weaknesses, and opportunities for differentiation.',
                'category' => 'business_intelligence',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            )
        );
    } elseif ($profile_type === 'creator') {
        $templates = array(
            'content_strategy' => array(
                'id' => 'default_content_strategy',
                'name' => 'Content Strategy Template',
                'prompt' => 'Help me develop a content strategy for [platform/audience]. Include content pillars, posting schedule, and engagement tactics for the African market.',
                'category' => 'content',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            'social_media_post' => array(
                'id' => 'default_social_media_post',
                'name' => 'Social Media Post Template',
                'prompt' => 'Create an engaging social media post about [topic] for [platform]. Make it culturally relevant for African audiences and include relevant hashtags.',
                'category' => 'content',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            'brand_development' => array(
                'id' => 'default_brand_development',
                'name' => 'Brand Development Template',
                'prompt' => 'Help me develop my personal/business brand for [niche/industry]. Include brand positioning, voice, and visual identity suggestions for the African market.',
                'category' => 'branding',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            'audience_growth' => array(
                'id' => 'default_audience_growth',
                'name' => 'Audience Growth Template',
                'prompt' => 'Suggest strategies to grow my audience on [platform] in [country/region]. Include content ideas, engagement tactics, and collaboration opportunities.',
                'category' => 'growth',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            )
        );
    }

    // Add industry-specific templates
    if ($industry) {
        $industry_templates = businesscraft_ai_get_industry_templates($industry);
        $templates = array_merge($templates, $industry_templates);
    }

    // Save templates to user meta
    update_user_meta($user_id, 'businesscraft_ai_prompt_templates', $templates);
}

/**
 * Get industry-specific templates
 */
function businesscraft_ai_get_industry_templates($industry) {
    $templates = array();

    switch ($industry) {
        case 'agriculture':
            $templates['agriculture_planning'] = array(
                'id' => 'industry_agriculture_planning',
                'name' => 'Agricultural Business Planning',
                'prompt' => 'Help me plan an agricultural business in [country]. Include crop selection, market analysis, supply chain considerations, and seasonal planning for African farming conditions.',
                'category' => 'industry_specific',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            );
            break;

        case 'technology':
            $templates['tech_startup'] = array(
                'id' => 'industry_tech_startup',
                'name' => 'Tech Startup Strategy',
                'prompt' => 'Help me develop a strategy for my tech startup in [country]. Include product development, market entry, funding options, and scaling strategies for the African tech ecosystem.',
                'category' => 'industry_specific',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            );
            break;

        case 'retail':
            $templates['retail_strategy'] = array(
                'id' => 'industry_retail_strategy',
                'name' => 'Retail Business Strategy',
                'prompt' => 'Help me develop a retail strategy for [product type] in [country]. Include location analysis, inventory management, pricing strategy, and customer acquisition for African markets.',
                'category' => 'industry_specific',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            );
            break;
    }

    return $templates;
}

/**
 * Include modular functionality
 */
// Core modules (new modular structure)
/**
 * Conditional module loading to optimize memory usage
 */
function chatgabi_load_modules_conditionally() {
    // Always load critical core modules
    $critical_modules = array(
        '/inc/headers-error-prevention.php',
        '/inc/database.php',
        '/inc/encryption.php',
        '/inc/template-functions.php'
    );

    foreach ($critical_modules as $module) {
        if (file_exists(CHATGABI_THEME_DIR . $module)) {
            require_once CHATGABI_THEME_DIR . $module;
        }
    }

    // Load memory optimizer first
    require_once CHATGABI_THEME_DIR . '/inc/memory-optimizer.php';

    // Load modules based on context
    if (is_admin()) {
        chatgabi_load_admin_modules();
    } elseif (wp_doing_ajax()) {
        chatgabi_load_ajax_modules();
    } elseif (defined('REST_REQUEST') && REST_REQUEST) {
        chatgabi_load_api_modules();
    } elseif (is_page() || is_front_page()) {
        chatgabi_load_frontend_modules();
    }
}

/**
 * Load admin-specific modules
 */
function chatgabi_load_admin_modules() {
    $admin_modules = array(
        '/inc/admin-dashboard.php',
        '/inc/admin-sector-analytics.php',
        '/inc/admin-analytics-extended.php',
        '/inc/feedback-admin.php',
        '/inc/admin-whatsapp.php',
        '/inc/admin-functions.php'
    );

    foreach ($admin_modules as $module) {
        if (file_exists(CHATGABI_THEME_DIR . $module)) {
            require_once CHATGABI_THEME_DIR . $module;
        }
    }
}

/**
 * Load frontend-specific modules based on page
 */
function chatgabi_load_frontend_modules() {
    // Basic frontend modules
    $frontend_modules = array(
        '/inc/language-functions.php',
        '/inc/language-strings.php',
        '/inc/user-preference-functions.php',
        '/inc/openai-integration.php',
        '/inc/paystack-integration.php'
    );

    // Load based on specific page needs
    $current_page = get_queried_object();
    if ($current_page && isset($current_page->post_name)) {
        switch ($current_page->post_name) {
            case 'dashboard':
                $frontend_modules = array_merge($frontend_modules, array(
                    '/inc/user-analytics.php',
                    '/inc/user-analytics-dashboard.php',
                    '/inc/context-personalization.php',
                    '/inc/prompt-templates.php',
                    '/inc/feedback-system.php',
                    '/inc/user-preferences.php',
                    '/inc/token-credit-tracker.php',
                    '/inc/user-credit-handlers.php'
                ));
                break;

            case 'templates':
                $frontend_modules = array_merge($frontend_modules, array(
                    '/inc/templates.php',
                    '/inc/template-management.php',
                    '/inc/template-customization.php',
                    '/inc/pdf-export.php'
                ));
                break;

            case 'preferences':
                $frontend_modules = array_merge($frontend_modules, array(
                    '/inc/user-preferences.php',
                    '/inc/user-preference-functions.php'
                ));
                break;
        }
    }

    foreach ($frontend_modules as $module) {
        if (file_exists(CHATGABI_THEME_DIR . $module)) {
            require_once CHATGABI_THEME_DIR . $module;
        }
    }
}

/**
 * Load API-specific modules
 */
function chatgabi_load_api_modules() {
    $api_modules = array(
        '/inc/rest-api.php',
        '/inc/api/opportunity-api.php',
        '/inc/openai-integration.php',
        '/inc/ajax-handlers.php'
    );

    foreach ($api_modules as $module) {
        if (file_exists(CHATGABI_THEME_DIR . $module)) {
            require_once CHATGABI_THEME_DIR . $module;
        }
    }
}

/**
 * Load AJAX-specific modules
 */
function chatgabi_load_ajax_modules() {
    $ajax_modules = array(
        '/inc/ajax-handlers.php',
        '/inc/openai-integration.php',
        '/inc/user-preferences.php',
        '/inc/feedback-system.php',
        '/inc/prompt-templates.php',
        '/inc/token-credit-tracker.php'
    );

    foreach ($ajax_modules as $module) {
        if (file_exists(CHATGABI_THEME_DIR . $module)) {
            require_once CHATGABI_THEME_DIR . $module;
        }
    }
}

// Initialize conditional loading
add_action('after_setup_theme', 'chatgabi_load_modules_conditionally', 5);

/**
 * Load additional modules conditionally based on features needed
 */
function chatgabi_load_feature_modules() {
    // Only load heavy modules when actually needed
    if (is_page('onboarding') || (isset($_GET['step']) && !empty($_GET['step']))) {
        require_once CHATGABI_THEME_DIR . '/inc/onboarding-system.php';
    }

    // Load export system only when needed
    if (isset($_GET['export']) || wp_doing_ajax() && isset($_POST['action']) && strpos($_POST['action'], 'export') !== false) {
        require_once CHATGABI_THEME_DIR . '/inc/export-system.php';
    }

    // Load collaboration features only on specific pages
    if (is_page('collaborate') || is_page('share')) {
        require_once CHATGABI_THEME_DIR . '/inc/collaboration.php';
    }

    // Load document wizards only when accessed
    if (is_page('wizards') || (isset($_GET['wizard']) && !empty($_GET['wizard']))) {
        require_once CHATGABI_THEME_DIR . '/inc/document-wizards.php';
    }

    // Load opportunity alerts only for logged-in users
    if (is_user_logged_in()) {
        require_once CHATGABI_THEME_DIR . '/inc/opportunity-alerts.php';
        require_once CHATGABI_THEME_DIR . '/inc/sendpulse-integration.php';
    }

    // Load credit purchase handlers only when needed
    if (is_page('purchase') || is_page('credits') || wp_doing_ajax() && isset($_POST['action']) && strpos($_POST['action'], 'purchase') !== false) {
        require_once CHATGABI_THEME_DIR . '/inc/credit-purchase-handlers.php';
    }
}

// Initialize feature modules loading
add_action('wp', 'chatgabi_load_feature_modules', 10);

// Initialize prompt templates system (conditional)
function chatgabi_init_prompt_templates_conditional() {
    if (is_page('templates') || is_page('dashboard') || wp_doing_ajax()) {
        if (function_exists('chatgabi_init_prompt_templates')) {
            chatgabi_init_prompt_templates();
        }
    }
}
add_action('wp', 'chatgabi_init_prompt_templates_conditional');

/**
 * Load advanced modules conditionally to save memory
 */
function chatgabi_load_advanced_modules() {
    // Load WhatsApp integration only for admin or when needed
    if (is_admin() || (isset($_GET['whatsapp']) && !empty($_GET['whatsapp']))) {
        require_once CHATGABI_THEME_DIR . '/inc/whatsapp-integration.php';
        if (is_admin()) {
            require_once CHATGABI_THEME_DIR . '/inc/admin-whatsapp.php';
        }
    }

    // Load translation service only when needed
    if (isset($_GET['translate']) || wp_doing_ajax() && isset($_POST['action']) && strpos($_POST['action'], 'translate') !== false) {
        require_once CHATGABI_THEME_DIR . '/inc/translation-service.php';
    }

    // Load sector data updater only for cron jobs or admin
    if (wp_doing_cron() || is_admin()) {
        require_once CHATGABI_THEME_DIR . '/inc/sector-data-updater.php';
    }

    // Load web scraping system only when needed (admin or cron)
    if (is_admin() || wp_doing_cron()) {
        require_once CHATGABI_THEME_DIR . '/inc/advanced-scraping-database.php';
        require_once CHATGABI_THEME_DIR . '/inc/scraping-infrastructure.php';
        require_once CHATGABI_THEME_DIR . '/inc/ai-agent-network.php';
        require_once CHATGABI_THEME_DIR . '/inc/data-quality-system.php';
        require_once CHATGABI_THEME_DIR . '/inc/expanded-data-sources.php';
        require_once CHATGABI_THEME_DIR . '/inc/advanced-web-scraper.php';
    }

    // Load performance enhancement modules
    require_once CHATGABI_THEME_DIR . '/inc/secure-api-key-manager.php';
    require_once CHATGABI_THEME_DIR . '/inc/enhanced-input-validator.php';

    // Load caching only if Redis is available
    if (class_exists('Redis') || extension_loaded('redis')) {
        require_once CHATGABI_THEME_DIR . '/inc/redis-caching.php';
    }

    // Load streaming and rate limiting for frontend
    if (!is_admin()) {
        require_once CHATGABI_THEME_DIR . '/inc/response-streaming.php';
        require_once CHATGABI_THEME_DIR . '/inc/advanced-rate-limiting.php';
    }

    // Load database optimization
    require_once CHATGABI_THEME_DIR . '/inc/database-optimization.php';

    // Load PWA support for frontend
    if (!is_admin()) {
        require_once CHATGABI_THEME_DIR . '/inc/pwa-support.php';
    }
}

/**
 * Load enhancement features conditionally
 */
function chatgabi_load_enhancement_features() {
    // Load AI feedback loops only for logged-in users
    if (is_user_logged_in() && !is_admin()) {
        require_once CHATGABI_THEME_DIR . '/inc/ai-feedback-loops.php';
    }

    // Load notification preferences only when needed
    if (is_page('preferences') || is_page('notifications')) {
        require_once CHATGABI_THEME_DIR . '/inc/notification-preferences.php';
    }

    // Load template AI enhancement only for templates page
    if (is_page('templates') || wp_doing_ajax() && isset($_POST['action']) && strpos($_POST['action'], 'template') !== false) {
        require_once CHATGABI_THEME_DIR . '/inc/template-ai-enhancement.php';
    }
}

// Initialize advanced modules loading
add_action('init', 'chatgabi_load_advanced_modules', 15);
add_action('wp', 'chatgabi_load_enhancement_features', 15);

// Include PDF export testing (only in development)
if (defined('WP_DEBUG') && WP_DEBUG) {
    require_once CHATGABI_THEME_DIR . '/test-pdf-export.php';
}

/**
 * Ensure REST API routes are registered
 */
function chatgabi_ensure_rest_routes() {
    // Add a simple health check route as backup
    register_rest_route('chatgabi/v1', '/health', array(
        'methods' => 'GET',
        'callback' => 'chatgabi_simple_health_check',
        'permission_callback' => '__return_true',
    ));

    // Log successful registration (only in debug mode)
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log("ChatGABI: REST API routes registered successfully");
    }

    // Note: Opportunity API routes are registered directly in opportunity-api.php
    // to avoid duplicate registrations
}
add_action('rest_api_init', 'chatgabi_ensure_rest_routes', 20); // Higher priority to ensure it runs after other registrations

/**
 * Simple health check endpoint
 */
function chatgabi_simple_health_check($request) {
    return new WP_REST_Response(array(
        'status' => 'healthy',
        'service' => 'ChatGABI AI',
        'version' => CHATGABI_VERSION,
        'timestamp' => current_time('c'),
        'message' => 'ChatGABI AI is running successfully'
    ), 200);
}

/**
 * Create sector logs table
 */
function chatgabi_create_sector_logs_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_sector_logs';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        timestamp datetime DEFAULT CURRENT_TIMESTAMP,
        country varchar(50) NOT NULL,
        detected_sector varchar(255) DEFAULT NULL,
        sector_context_found tinyint(1) DEFAULT 0,
        opportunities_included int(11) DEFAULT 0,
        opportunities_tokens_estimated int(11) DEFAULT 0,
        prompt_tokens_estimated int(11) DEFAULT 0,
        user_message_preview text,
        response_quality_rating int(1) DEFAULT NULL,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY timestamp (timestamp),
        KEY country (country),
        KEY detected_sector (detected_sector),
        KEY opportunities_included (opportunities_included)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Log sector context injection attempt with opportunity tracking
 */
function chatgabi_log_sector_context($user_id, $country, $detected_sector, $sector_context_found, $prompt_tokens, $user_message, $opportunities_included = 0, $opportunities_tokens = 0) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'chatgabi_sector_logs';

    // Truncate message preview to 100 characters
    $message_preview = substr($user_message, 0, 100);

    $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'timestamp' => current_time('mysql'),
            'country' => $country,
            'detected_sector' => $detected_sector,
            'sector_context_found' => $sector_context_found ? 1 : 0,
            'opportunities_included' => $opportunities_included,
            'opportunities_tokens_estimated' => $opportunities_tokens,
            'prompt_tokens_estimated' => $prompt_tokens,
            'user_message_preview' => $message_preview
        ),
        array(
            '%d',
            '%s',
            '%s',
            '%s',
            '%d',
            '%d',
            '%d',
            '%d',
            '%s'
        )
    );
}

/**
 * Cleanup old sector logs (90+ days)
 */
function businesscraft_ai_cleanup_sector_logs() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'businesscraft_ai_sector_logs';

    $wpdb->query($wpdb->prepare(
        "DELETE FROM $table_name WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY)"
    ));
}
add_action('businesscraft_ai_cleanup_sector_logs', 'businesscraft_ai_cleanup_sector_logs');

/**
 * Theme activation
 */
function chatgabi_activation() {
    // Create database tables
    businesscraft_ai_create_tables();
    chatgabi_create_sector_logs_table();

    // Create opportunity alerts tables
    if (function_exists('chatgabi_get_opportunity_alerts')) {
        $alerts_manager = chatgabi_get_opportunity_alerts();
        $alerts_manager->create_tables();
    }

    // Set default options
    add_option('businesscraft_ai_openai_api_key', '');
    add_option('businesscraft_ai_paystack_public_key', '');
    add_option('businesscraft_ai_paystack_secret_key', '');
    add_option('businesscraft_ai_encryption_key', wp_generate_password(32, false));

    // Opportunity alerts options
    add_option('chatgabi_sendpulse_user_id', '');
    add_option('chatgabi_sendpulse_secret', '');
    add_option('chatgabi_sendpulse_template_id', '');
    add_option('chatgabi_alerts_enabled', 1);
    add_option('chatgabi_max_alerts_per_user', 10);
    add_option('chatgabi_alert_rate_limit', 100);

    // Schedule cleanup cron job
    if (!wp_next_scheduled('businesscraft_ai_cleanup_sector_logs')) {
        wp_schedule_event(time(), 'daily', 'businesscraft_ai_cleanup_sector_logs');
    }

    // Schedule opportunity alerts cron jobs (will be handled by the alerts system itself)
    // The opportunity alerts system will schedule its own cron jobs with proper custom schedules

    // Flush rewrite rules to ensure REST API routes are registered
    flush_rewrite_rules();

    // Force REST API route registration
    do_action('rest_api_init');
}
add_action('after_switch_theme', 'businesscraft_ai_activation');

/**
 * Add test webhook endpoint for development
 */
function businesscraft_ai_add_test_webhook_endpoint() {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        add_action('wp_ajax_test_paystack_webhook', 'businesscraft_ai_test_webhook_handler');
        add_action('wp_ajax_nopriv_test_paystack_webhook', 'businesscraft_ai_test_webhook_handler');
    }
}
add_action('init', 'businesscraft_ai_add_test_webhook_endpoint');

/**
 * Test webhook handler for development
 */
function businesscraft_ai_test_webhook_handler() {
    if (!defined('WP_DEBUG') || !WP_DEBUG) {
        wp_die('Test endpoint only available in debug mode');
    }

    // Create test webhook data
    $test_data = array(
        'event' => 'charge.success',
        'data' => array(
            'id' => 123456789,
            'reference' => 'test_' . time(),
            'amount' => 6000, // 60 GHS in pesewas
            'currency' => 'GHS',
            'status' => 'success',
            'customer' => array(
                'email' => '<EMAIL>'
            ),
            'metadata' => array(
                'user_id' => get_current_user_id(),
                'package' => 'starter',
                'credits' => 500,
                'original_currency' => 'GHS',
                'original_amount' => 60.00,
                'usd_amount' => 5.00
            )
        )
    );

    // Simulate webhook request
    $request = new WP_REST_Request('POST', '/bcai/v1/paystack-webhook');
    $request->set_body(json_encode($test_data));
    $request->set_header('content-type', 'application/json');

    // For testing, we'll skip signature verification by temporarily modifying the function
    add_filter('businesscraft_ai_skip_webhook_signature_verification', '__return_true');

    // Call the webhook handler
    $response = businesscraft_ai_paystack_webhook($request);

    // Remove the filter
    remove_filter('businesscraft_ai_skip_webhook_signature_verification', '__return_true');

    if (is_wp_error($response)) {
        wp_send_json_error(array(
            'message' => $response->get_error_message(),
            'code' => $response->get_error_code()
        ));
    } else {
        wp_send_json_success(array(
            'message' => 'Webhook test successful',
            'response' => $response
        ));
    }
}

















