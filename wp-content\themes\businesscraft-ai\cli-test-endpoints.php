<?php
/**
 * CLI Test for AI Widget REST API Endpoints
 * 
 * Run this from command line: php cli-test-endpoints.php
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Load WordPress
require_once('../../../wp-load.php');

echo "ChatGABI AI Widget Endpoints CLI Test\n";
echo "=====================================\n\n";

/**
 * Test 1: Check if REST API routes are registered
 */
echo "1. REST API Route Registration:\n";
echo "-------------------------------\n";

$rest_server = rest_get_server();
$routes = $rest_server->get_routes();

$ai_widget_endpoints = array(
    'chatgabi/v1/ai-widgets/suggestions',
    'chatgabi/v1/ai-widgets/enhance',
    'chatgabi/v1/ai-widgets/real-time-suggestions'
);

foreach ($ai_widget_endpoints as $endpoint) {
    $route_key = '/' . $endpoint;
    if (isset($routes[$route_key])) {
        echo "✅ Endpoint '$endpoint' is registered\n";
        
        // Check callback function
        $route_data = $routes[$route_key][0];
        $callback = $route_data['callback'];
        if (is_callable($callback)) {
            echo "   → Callback function '$callback' is callable\n";
        } else {
            echo "   ❌ Callback function '$callback' is not callable\n";
        }
    } else {
        echo "❌ Endpoint '$endpoint' is NOT registered\n";
    }
}

echo "\n";

/**
 * Test 2: Check callback functions exist
 */
echo "2. Callback Function Availability:\n";
echo "----------------------------------\n";

$callback_functions = array(
    'chatgabi_get_ai_field_suggestions',
    'chatgabi_enhance_field_content',
    'chatgabi_get_real_time_suggestions'
);

foreach ($callback_functions as $function) {
    if (function_exists($function)) {
        echo "✅ Function '$function' exists\n";
    } else {
        echo "❌ Function '$function' does NOT exist\n";
    }
}

echo "\n";

/**
 * Test 3: Check supporting functions
 */
echo "3. Supporting Function Availability:\n";
echo "------------------------------------\n";

$supporting_functions = array(
    'chatgabi_get_field_suggestions',
    'chatgabi_get_contextual_suggestions',
    'chatgabi_get_african_market_examples',
    'chatgabi_get_real_time_field_suggestions',
    'chatgabi_process_ai_field_enhancement'
);

foreach ($supporting_functions as $function) {
    if (function_exists($function)) {
        echo "✅ Function '$function' exists\n";
    } else {
        echo "❌ Function '$function' does NOT exist\n";
    }
}

echo "\n";

/**
 * Test 4: Check permission functions
 */
echo "4. Permission Function Availability:\n";
echo "------------------------------------\n";

$permission_functions = array(
    'chatgabi_check_user_permission'
);

foreach ($permission_functions as $function) {
    if (function_exists($function)) {
        echo "✅ Function '$function' exists\n";
    } else {
        echo "❌ Function '$function' does NOT exist\n";
    }
}

echo "\n";

/**
 * Test 5: Check WordPress hooks
 */
echo "5. WordPress Hook Registration:\n";
echo "-------------------------------\n";

global $wp_filter;

if (isset($wp_filter['rest_api_init'])) {
    $rest_api_hooks = $wp_filter['rest_api_init'];
    $found_hook = false;
    
    foreach ($rest_api_hooks->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback_id => $callback_data) {
            if (strpos($callback_id, 'chatgabi_register_rest_routes') !== false) {
                echo "✅ 'chatgabi_register_rest_routes' hook is registered with priority $priority\n";
                $found_hook = true;
                break 2;
            }
        }
    }
    
    if (!$found_hook) {
        echo "❌ 'chatgabi_register_rest_routes' hook is NOT registered\n";
    }
} else {
    echo "❌ No 'rest_api_init' hooks found\n";
}

echo "\n";

/**
 * Test 6: Check if files are included
 */
echo "6. File Inclusion Check:\n";
echo "------------------------\n";

$required_files = array(
    'inc/rest-api.php',
    'inc/ai-template-widgets.php'
);

foreach ($required_files as $file) {
    $file_path = get_template_directory() . '/' . $file;
    if (file_exists($file_path)) {
        echo "✅ File '$file' exists\n";
    } else {
        echo "❌ File '$file' does NOT exist\n";
    }
}

echo "\n";

/**
 * Test Summary
 */
echo "Test Summary:\n";
echo "=============\n";
echo "Test completed at: " . current_time('mysql') . "\n";
echo "WordPress Version: " . get_bloginfo('version') . "\n";
echo "Theme: " . get_template() . "\n";

// Count registered routes
$total_routes = count($routes);
$chatgabi_routes = 0;
foreach ($routes as $route => $data) {
    if (strpos($route, '/chatgabi/v1/') !== false) {
        $chatgabi_routes++;
    }
}

echo "Total REST routes: $total_routes\n";
echo "ChatGABI routes: $chatgabi_routes\n";

echo "\nTest completed successfully!\n";
?>
