<?php
/**
 * Test AI Widget REST API Endpoints
 * 
 * This script tests the ChatGABI AI widget REST API endpoints to ensure they are properly registered and accessible.
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-load.php');
}

// Set content type
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI AI Widget Endpoints Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 3px; }
        .pass { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .fail { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; padding: 8px; margin: 5px 0; border-radius: 3px; }
        .endpoint-test { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>

<h1>ChatGABI AI Widget Endpoints Test</h1>
<p>Testing the REST API endpoints for AI widget functionality.</p>

<?php

/**
 * Test 1: Check if REST API routes are registered
 */
echo "<div class='test-section'>\n";
echo "<h2>Test 1: REST API Route Registration</h2>\n";

$rest_server = rest_get_server();
$routes = $rest_server->get_routes();

$ai_widget_endpoints = array(
    'chatgabi/v1/ai-widgets/suggestions',
    'chatgabi/v1/ai-widgets/enhance',
    'chatgabi/v1/ai-widgets/real-time-suggestions'
);

foreach ($ai_widget_endpoints as $endpoint) {
    $route_key = '/' . $endpoint;
    if (isset($routes[$route_key])) {
        echo "<div class='test-result pass'>✅ Endpoint '$endpoint' is registered</div>\n";
        
        // Check callback function
        $route_data = $routes[$route_key][0];
        $callback = $route_data['callback'];
        if (is_callable($callback)) {
            echo "<div class='info'>Callback function '$callback' is callable</div>\n";
        } else {
            echo "<div class='test-result fail'>❌ Callback function '$callback' is not callable</div>\n";
        }
    } else {
        echo "<div class='test-result fail'>❌ Endpoint '$endpoint' is NOT registered</div>\n";
    }
}

echo "</div>\n";

/**
 * Test 2: Check callback functions exist
 */
echo "<div class='test-section'>\n";
echo "<h2>Test 2: Callback Function Availability</h2>\n";

$callback_functions = array(
    'chatgabi_get_ai_field_suggestions',
    'chatgabi_enhance_field_content',
    'chatgabi_get_real_time_suggestions'
);

foreach ($callback_functions as $function) {
    if (function_exists($function)) {
        echo "<div class='test-result pass'>✅ Function '$function' exists</div>\n";
    } else {
        echo "<div class='test-result fail'>❌ Function '$function' does NOT exist</div>\n";
    }
}

echo "</div>\n";

/**
 * Test 3: Check supporting functions
 */
echo "<div class='test-section'>\n";
echo "<h2>Test 3: Supporting Function Availability</h2>\n";

$supporting_functions = array(
    'chatgabi_get_field_suggestions',
    'chatgabi_get_contextual_suggestions',
    'chatgabi_get_african_market_examples',
    'chatgabi_get_real_time_field_suggestions',
    'chatgabi_process_ai_field_enhancement'
);

foreach ($supporting_functions as $function) {
    if (function_exists($function)) {
        echo "<div class='test-result pass'>✅ Function '$function' exists</div>\n";
    } else {
        echo "<div class='test-result fail'>❌ Function '$function' does NOT exist</div>\n";
    }
}

echo "</div>\n";

/**
 * Test 4: Check permission functions
 */
echo "<div class='test-section'>\n";
echo "<h2>Test 4: Permission Function Availability</h2>\n";

$permission_functions = array(
    'chatgabi_check_user_permission'
);

foreach ($permission_functions as $function) {
    if (function_exists($function)) {
        echo "<div class='test-result pass'>✅ Function '$function' exists</div>\n";
    } else {
        echo "<div class='test-result fail'>❌ Function '$function' does NOT exist</div>\n";
    }
}

echo "</div>\n";

/**
 * Test 5: Test REST API endpoint accessibility (if user is logged in)
 */
echo "<div class='test-section'>\n";
echo "<h2>Test 5: Endpoint Accessibility Test</h2>\n";

if (is_user_logged_in()) {
    echo "<div class='info'>User is logged in. Testing endpoint accessibility...</div>\n";
    
    // Test suggestions endpoint
    echo "<div class='endpoint-test'>\n";
    echo "<h3>Testing /ai-widgets/suggestions</h3>\n";
    
    $request = new WP_REST_Request('POST', '/chatgabi/v1/ai-widgets/suggestions');
    $request->set_param('field_type', 'business_description');
    $request->set_param('field_content', 'Test content');
    $request->set_param('user_context', array('country' => 'GH', 'industry' => 'technology'));
    
    $response = rest_do_request($request);
    
    if ($response->is_error()) {
        $error = $response->as_error();
        echo "<div class='test-result fail'>❌ Error: " . $error->get_error_message() . "</div>\n";
    } else {
        $data = $response->get_data();
        echo "<div class='test-result pass'>✅ Endpoint responded successfully</div>\n";
        echo "<div class='info'>Response: <pre>" . json_encode($data, JSON_PRETTY_PRINT) . "</pre></div>\n";
    }
    echo "</div>\n";
    
} else {
    echo "<div class='info'>User is not logged in. Cannot test endpoint accessibility (requires authentication).</div>\n";
    echo "<div class='info'>To test endpoint accessibility, please log in to WordPress and refresh this page.</div>\n";
}

echo "</div>\n";

/**
 * Test 6: Check WordPress hooks
 */
echo "<div class='test-section'>\n";
echo "<h2>Test 6: WordPress Hook Registration</h2>\n";

global $wp_filter;

if (isset($wp_filter['rest_api_init'])) {
    $rest_api_hooks = $wp_filter['rest_api_init'];
    $found_hook = false;
    
    foreach ($rest_api_hooks->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback_id => $callback_data) {
            if (strpos($callback_id, 'chatgabi_register_rest_routes') !== false) {
                echo "<div class='test-result pass'>✅ 'chatgabi_register_rest_routes' hook is registered with priority $priority</div>\n";
                $found_hook = true;
                break 2;
            }
        }
    }
    
    if (!$found_hook) {
        echo "<div class='test-result fail'>❌ 'chatgabi_register_rest_routes' hook is NOT registered</div>\n";
    }
} else {
    echo "<div class='test-result fail'>❌ No 'rest_api_init' hooks found</div>\n";
}

echo "</div>\n";

/**
 * Test Summary
 */
echo "<div class='test-section'>\n";
echo "<h2>Test Summary</h2>\n";
echo "<div class='info'>Test completed at: " . current_time('mysql') . "</div>\n";
echo "<div class='info'>WordPress Version: " . get_bloginfo('version') . "</div>\n";
echo "<div class='info'>Theme: " . get_template() . "</div>\n";
echo "<div class='info'>Current User: " . (is_user_logged_in() ? wp_get_current_user()->user_login : 'Not logged in') . "</div>\n";
echo "</div>\n";

?>

</body>
</html>
