<?php
/**
 * AI-Assisted Context-Aware Template Widgets
 * Embedded AI assistance for template fields and customization
 * 
 * @package ChatGABI
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Generate AI assistance widget for template fields
 */
function chatgabi_generate_ai_widget($field_type, $field_context, $user_context = array()) {
    $widget_id = 'ai-widget-' . uniqid();
    
    $html = '<div class="chatgabi-ai-widget" data-widget-id="' . esc_attr($widget_id) . '" data-field-type="' . esc_attr($field_type) . '">';
    
    // Widget header
    $html .= '<div class="ai-widget-header">';
    $html .= '<span class="ai-widget-icon">🤖</span>';
    $html .= '<span class="ai-widget-title">' . __('AI Assistant', 'chatgabi') . '</span>';
    $html .= '<button class="ai-widget-toggle" onclick="toggleAIWidget(\'' . $widget_id . '\')">';
    $html .= '<span class="toggle-icon">▼</span>';
    $html .= '</button>';
    $html .= '</div>';
    
    // Widget content
    $html .= '<div class="ai-widget-content" id="' . esc_attr($widget_id) . '-content">';
    
    // Field-specific AI suggestions
    $html .= chatgabi_generate_field_suggestions($field_type, $field_context, $user_context);
    
    // AI enhancement options
    $html .= '<div class="ai-enhancement-options">';
    $html .= '<h4>' . __('AI Enhancement Options', 'chatgabi') . '</h4>';
    $html .= '<div class="enhancement-buttons">';
    
    switch ($field_type) {
        case 'business_description':
            $html .= '<button class="ai-enhance-btn" data-action="improve_description" data-field="' . esc_attr($field_context['field_id']) . '">';
            $html .= '✨ ' . __('Improve Description', 'chatgabi');
            $html .= '</button>';
            $html .= '<button class="ai-enhance-btn" data-action="add_local_context" data-field="' . esc_attr($field_context['field_id']) . '">';
            $html .= '🌍 ' . __('Add Local Context', 'chatgabi');
            $html .= '</button>';
            break;
            
        case 'market_analysis':
            $html .= '<button class="ai-enhance-btn" data-action="generate_market_data" data-field="' . esc_attr($field_context['field_id']) . '">';
            $html .= '📊 ' . __('Generate Market Data', 'chatgabi');
            $html .= '</button>';
            $html .= '<button class="ai-enhance-btn" data-action="competitor_analysis" data-field="' . esc_attr($field_context['field_id']) . '">';
            $html .= '🏢 ' . __('Competitor Analysis', 'chatgabi');
            $html .= '</button>';
            break;
            
        case 'financial_projections':
            $html .= '<button class="ai-enhance-btn" data-action="generate_projections" data-field="' . esc_attr($field_context['field_id']) . '">';
            $html .= '💰 ' . __('Generate Projections', 'chatgabi');
            $html .= '</button>';
            $html .= '<button class="ai-enhance-btn" data-action="validate_assumptions" data-field="' . esc_attr($field_context['field_id']) . '">';
            $html .= '✅ ' . __('Validate Assumptions', 'chatgabi');
            $html .= '</button>';
            break;
            
        default:
            $html .= '<button class="ai-enhance-btn" data-action="improve_content" data-field="' . esc_attr($field_context['field_id']) . '">';
            $html .= '🚀 ' . __('Enhance Content', 'chatgabi');
            $html .= '</button>';
    }
    
    $html .= '</div>';
    $html .= '</div>';
    
    // AI suggestions display
    $html .= '<div class="ai-suggestions-display" id="' . esc_attr($widget_id) . '-suggestions">';
    $html .= '<div class="suggestions-placeholder">';
    $html .= '<p>' . __('Click an enhancement button to get AI-powered suggestions for this field.', 'chatgabi') . '</p>';
    $html .= '</div>';
    $html .= '</div>';
    
    // Credit cost display
    $html .= '<div class="ai-widget-footer">';
    $html .= '<div class="credit-cost-info">';
    $html .= '<span class="cost-label">' . __('Enhancement Cost:', 'chatgabi') . '</span>';
    $html .= '<span class="cost-amount">1-2 ' . __('credits', 'chatgabi') . '</span>';
    $html .= '</div>';
    $html .= '</div>';
    
    $html .= '</div>'; // Close ai-widget-content
    $html .= '</div>'; // Close chatgabi-ai-widget
    
    return $html;
}

/**
 * Generate field-specific AI suggestions
 */
function chatgabi_generate_field_suggestions($field_type, $field_context, $user_context) {
    $html = '<div class="field-suggestions">';
    $html .= '<h4>' . __('Smart Suggestions', 'chatgabi') . '</h4>';
    
    $suggestions = chatgabi_get_field_suggestions($field_type, $user_context);
    
    if (!empty($suggestions)) {
        $html .= '<div class="suggestions-list">';
        foreach ($suggestions as $suggestion) {
            $html .= '<div class="suggestion-item" onclick="applySuggestion(\'' . esc_js($suggestion['text']) . '\', \'' . esc_attr($field_context['field_id']) . '\')">';
            $html .= '<div class="suggestion-text">' . esc_html($suggestion['text']) . '</div>';
            $html .= '<div class="suggestion-meta">';
            $html .= '<span class="suggestion-type">' . esc_html($suggestion['type']) . '</span>';
            if (isset($suggestion['confidence'])) {
                $html .= '<span class="suggestion-confidence">' . esc_html($suggestion['confidence']) . '% match</span>';
            }
            $html .= '</div>';
            $html .= '</div>';
        }
        $html .= '</div>';
    } else {
        $html .= '<p class="no-suggestions">' . __('No suggestions available for this field type.', 'chatgabi') . '</p>';
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Get AI-powered field suggestions based on user context
 */
function chatgabi_get_field_suggestions($field_type, $user_context) {
    $user_country = $user_context['country'] ?? 'GH';
    $user_industry = $user_context['industry'] ?? 'general';
    
    // Get African Context Engine suggestions
    $suggestions = array();
    
    if (class_exists('BusinessCraft_African_Context_Engine')) {
        $african_context = new BusinessCraft_African_Context_Engine();
        $market_examples = $african_context->generate_market_examples($user_country, $user_industry);
        
        switch ($field_type) {
            case 'business_description':
                $suggestions = array(
                    array(
                        'text' => 'Innovative ' . $user_industry . ' solution designed for the ' . chatgabi_get_country_name_from_code($user_country) . ' market',
                        'type' => 'Template',
                        'confidence' => 85
                    ),
                    array(
                        'text' => 'Addressing key challenges in ' . $user_industry . ' sector across Africa',
                        'type' => 'Focus Area',
                        'confidence' => 90
                    ),
                    array(
                        'text' => 'Leveraging local partnerships and cultural understanding',
                        'type' => 'Competitive Advantage',
                        'confidence' => 80
                    )
                );
                break;
                
            case 'market_analysis':
                $suggestions = array(
                    array(
                        'text' => 'Growing ' . $user_industry . ' market in ' . chatgabi_get_country_name_from_code($user_country) . ' with 15-20% annual growth',
                        'type' => 'Market Size',
                        'confidence' => 75
                    ),
                    array(
                        'text' => 'Target demographics: Urban middle class, SME businesses, tech-savvy consumers',
                        'type' => 'Target Market',
                        'confidence' => 85
                    ),
                    array(
                        'text' => 'Key competitors: ' . implode(', ', array_slice($market_examples['main_competitors'] ?? array(), 0, 3)),
                        'type' => 'Competition',
                        'confidence' => 70
                    )
                );
                break;
                
            case 'financial_projections':
                $currency = chatgabi_get_country_currency($user_country);
                $suggestions = array(
                    array(
                        'text' => 'Initial investment: ' . $currency . ' 50,000 - 100,000',
                        'type' => 'Startup Capital',
                        'confidence' => 80
                    ),
                    array(
                        'text' => 'Break-even timeline: 12-18 months',
                        'type' => 'Timeline',
                        'confidence' => 75
                    ),
                    array(
                        'text' => 'Year 3 revenue target: ' . $currency . ' 500,000 - 1,000,000',
                        'type' => 'Revenue Goal',
                        'confidence' => 70
                    )
                );
                break;
        }
    }
    
    return $suggestions;
}

/**
 * Process AI enhancement request for template fields
 */
function chatgabi_process_ai_field_enhancement($field_id, $action, $current_content, $user_context) {
    // Check user credits
    $user_id = get_current_user_id();
    $current_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;
    $required_credits = chatgabi_get_enhancement_credit_cost($action);
    
    if ($current_credits < $required_credits) {
        return new WP_Error(
            'insufficient_credits',
            sprintf(__('Insufficient credits. Required: %d, Available: %d', 'chatgabi'), $required_credits, $current_credits)
        );
    }
    
    // Build enhancement prompt
    $prompt = chatgabi_build_field_enhancement_prompt($action, $current_content, $user_context);
    
    // Call OpenAI API
    $response = businesscraft_ai_process_openai_request($prompt, $user_id, array(
        'max_tokens' => 400,
        'temperature' => 0.7
    ));
    
    if (is_wp_error($response)) {
        return $response;
    }
    
    // Deduct credits
    $new_credits = $current_credits - $required_credits;
    update_user_meta($user_id, 'businesscraft_credits', $new_credits);
    
    // Log enhancement usage
    chatgabi_log_ai_enhancement_usage($user_id, $field_id, $action, $required_credits);
    
    return array(
        'enhanced_content' => $response['response'],
        'original_content' => $current_content,
        'enhancement_type' => $action,
        'credits_used' => $required_credits,
        'remaining_credits' => $new_credits,
        'suggestions' => chatgabi_extract_enhancement_suggestions($response['response'])
    );
}

/**
 * Build enhancement prompt for specific field actions
 */
function chatgabi_build_field_enhancement_prompt($action, $content, $user_context) {
    $country_name = chatgabi_get_country_name_from_code($user_context['country'] ?? 'GH');
    $industry = $user_context['industry'] ?? 'general business';
    
    $base_context = "You are an expert business consultant specializing in African markets, particularly {$country_name}. ";
    $base_context .= "You have deep knowledge of the {$industry} industry and local business practices. ";
    
    switch ($action) {
        case 'improve_description':
            return $base_context . "Improve this business description to be more compelling and specific to the {$country_name} market:\n\n{$content}\n\nProvide an enhanced version that highlights unique value propositions and local market relevance.";
            
        case 'add_local_context':
            return $base_context . "Add specific local context and cultural considerations to this content for the {$country_name} market:\n\n{$content}\n\nInclude relevant local business practices, cultural nuances, and market-specific opportunities.";
            
        case 'generate_market_data':
            return $base_context . "Generate realistic market analysis data for the {$industry} industry in {$country_name}. Include market size, growth rates, key segments, and trends. Base this on:\n\n{$content}";
            
        case 'competitor_analysis':
            return $base_context . "Provide a competitive landscape analysis for the {$industry} sector in {$country_name}. Include major players, market positioning, and competitive advantages. Context:\n\n{$content}";
            
        case 'generate_projections':
            return $base_context . "Create realistic financial projections for a {$industry} business in {$country_name}. Include revenue forecasts, expense estimates, and key assumptions. Business context:\n\n{$content}";
            
        case 'validate_assumptions':
            return $base_context . "Review and validate these financial assumptions for a {$industry} business in {$country_name}. Provide feedback on realism and suggest improvements:\n\n{$content}";
            
        default:
            return $base_context . "Enhance this content to be more professional, detailed, and relevant to the {$country_name} {$industry} market:\n\n{$content}";
    }
}

/**
 * Get credit cost for different enhancement actions
 */
function chatgabi_get_enhancement_credit_cost($action) {
    $costs = array(
        'improve_description' => 1,
        'add_local_context' => 1,
        'generate_market_data' => 2,
        'competitor_analysis' => 2,
        'generate_projections' => 2,
        'validate_assumptions' => 1,
        'improve_content' => 1
    );
    
    return $costs[$action] ?? 1;
}

/**
 * Extract actionable suggestions from AI response
 */
function chatgabi_extract_enhancement_suggestions($ai_response) {
    // Simple extraction of bullet points or numbered lists
    $suggestions = array();
    
    // Look for bullet points
    if (preg_match_all('/[•\-\*]\s*(.+)/', $ai_response, $matches)) {
        foreach ($matches[1] as $suggestion) {
            $suggestions[] = array(
                'text' => trim($suggestion),
                'type' => 'AI Suggestion'
            );
        }
    }
    
    // Look for numbered lists
    if (preg_match_all('/\d+\.\s*(.+)/', $ai_response, $matches)) {
        foreach ($matches[1] as $suggestion) {
            $suggestions[] = array(
                'text' => trim($suggestion),
                'type' => 'AI Recommendation'
            );
        }
    }
    
    return array_slice($suggestions, 0, 5); // Limit to 5 suggestions
}

/**
 * Log AI enhancement usage for analytics
 */
function chatgabi_log_ai_enhancement_usage($user_id, $field_id, $action, $credits_used) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'chatgabi_ai_enhancement_log';
    
    $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'field_id' => $field_id,
            'enhancement_action' => $action,
            'credits_used' => $credits_used,
            'created_at' => current_time('mysql')
        ),
        array('%d', '%s', '%s', '%d', '%s')
    );
}

// Note: chatgabi_get_country_currency() function is defined in template-preview-system.php

/**
 * Get contextual suggestions based on current field content
 */
function chatgabi_get_contextual_suggestions($field_type, $field_content, $user_context) {
    $suggestions = array();
    $content_length = strlen($field_content);
    $words = str_word_count($field_content);

    // Analyze content and provide contextual suggestions
    switch ($field_type) {
        case 'business_description':
            if ($content_length < 50) {
                $suggestions[] = array(
                    'text' => 'Consider adding your target market and unique value proposition',
                    'type' => 'Content Guidance',
                    'confidence' => 90
                );
            }
            if (strpos(strtolower($field_content), 'africa') === false) {
                $suggestions[] = array(
                    'text' => 'Highlight your African market focus and local understanding',
                    'type' => 'Local Context',
                    'confidence' => 85
                );
            }
            break;

        case 'market_analysis':
            if (strpos(strtolower($field_content), 'competitor') === false) {
                $suggestions[] = array(
                    'text' => 'Include competitive landscape analysis',
                    'type' => 'Market Research',
                    'confidence' => 80
                );
            }
            break;

        case 'financial_projections':
            if (strpos($field_content, chatgabi_get_country_currency($user_context['country'])) === false) {
                $currency = chatgabi_get_country_currency($user_context['country']);
                $suggestions[] = array(
                    'text' => "Consider using local currency ({$currency}) for projections",
                    'type' => 'Currency Guidance',
                    'confidence' => 75
                );
            }
            break;
    }

    return $suggestions;
}

/**
 * Get African market examples for field types
 */
function chatgabi_get_african_market_examples($field_type, $user_context) {
    $country = $user_context['country'] ?? 'GH';
    $industry = $user_context['industry'] ?? 'general';

    // Get examples from African Context Engine
    if (class_exists('BusinessCraft_African_Context_Engine')) {
        $african_context = new BusinessCraft_African_Context_Engine();
        $market_examples = $african_context->generate_market_examples($country, $industry);

        switch ($field_type) {
            case 'business_description':
                return array(
                    'successful_businesses' => array_slice($market_examples['successful_businesses']['sme_success_stories'] ?? array(), 0, 3),
                    'cultural_considerations' => $market_examples['cultural_considerations'] ?? array(),
                    'success_factors' => array_slice($market_examples['success_factors'] ?? array(), 0, 3)
                );

            case 'market_analysis':
                return array(
                    'payment_methods' => $market_examples['payment_examples'] ?? array(),
                    'marketing_channels' => $market_examples['marketing_channels'] ?? array(),
                    'business_registration' => $market_examples['business_registration'] ?? ''
                );

            case 'financial_projections':
                return array(
                    'currency_info' => chatgabi_get_country_currency($country),
                    'typical_costs' => chatgabi_get_typical_business_costs($country, $industry),
                    'funding_sources' => chatgabi_get_funding_sources($country)
                );
        }
    }

    return array();
}

/**
 * Get real-time field suggestions based on partial content
 */
function chatgabi_get_real_time_field_suggestions($field_type, $partial_content, $user_context) {
    $suggestions = array();
    $last_words = array_slice(str_word_count($partial_content, 1), -3);
    $last_phrase = implode(' ', $last_words);

    // Generate completion suggestions based on context
    switch ($field_type) {
        case 'business_description':
            if (stripos($last_phrase, 'target') !== false) {
                $suggestions[] = array(
                    'text' => 'target market includes urban professionals and SME businesses',
                    'type' => 'Auto-complete',
                    'confidence' => 85
                );
            }
            if (stripos($last_phrase, 'solution') !== false) {
                $suggestions[] = array(
                    'text' => 'solution addresses key challenges in the ' . $user_context['industry'] . ' sector',
                    'type' => 'Auto-complete',
                    'confidence' => 80
                );
            }
            break;

        case 'market_analysis':
            if (stripos($last_phrase, 'market size') !== false) {
                $country_name = chatgabi_get_country_name_from_code($user_context['country']);
                $suggestions[] = array(
                    'text' => "market size in {$country_name} is estimated at...",
                    'type' => 'Auto-complete',
                    'confidence' => 75
                );
            }
            break;

        case 'financial_projections':
            if (stripos($last_phrase, 'revenue') !== false) {
                $currency = chatgabi_get_country_currency($user_context['country']);
                $suggestions[] = array(
                    'text' => "revenue projections for Year 1: {$currency} 100,000",
                    'type' => 'Auto-complete',
                    'confidence' => 70
                );
            }
            break;
    }

    return $suggestions;
}

/**
 * Get typical business costs for a country and industry
 */
function chatgabi_get_typical_business_costs($country, $industry) {
    $costs = array(
        'GH' => array(
            'technology' => array('startup' => 'GHS 20,000 - 50,000', 'monthly' => 'GHS 5,000 - 15,000'),
            'retail' => array('startup' => 'GHS 15,000 - 40,000', 'monthly' => 'GHS 8,000 - 20,000'),
            'services' => array('startup' => 'GHS 10,000 - 25,000', 'monthly' => 'GHS 3,000 - 10,000')
        ),
        'KE' => array(
            'technology' => array('startup' => 'KES 500,000 - 1,200,000', 'monthly' => 'KES 150,000 - 400,000'),
            'retail' => array('startup' => 'KES 300,000 - 800,000', 'monthly' => 'KES 200,000 - 500,000'),
            'services' => array('startup' => 'KES 200,000 - 600,000', 'monthly' => 'KES 100,000 - 300,000')
        ),
        'NG' => array(
            'technology' => array('startup' => 'NGN 2,000,000 - 5,000,000', 'monthly' => 'NGN 500,000 - 1,500,000'),
            'retail' => array('startup' => 'NGN 1,500,000 - 4,000,000', 'monthly' => 'NGN 800,000 - 2,000,000'),
            'services' => array('startup' => 'NGN 1,000,000 - 3,000,000', 'monthly' => 'NGN 300,000 - 1,000,000')
        ),
        'ZA' => array(
            'technology' => array('startup' => 'ZAR 100,000 - 250,000', 'monthly' => 'ZAR 30,000 - 80,000'),
            'retail' => array('startup' => 'ZAR 80,000 - 200,000', 'monthly' => 'ZAR 40,000 - 100,000'),
            'services' => array('startup' => 'ZAR 50,000 - 150,000', 'monthly' => 'ZAR 20,000 - 60,000')
        )
    );

    return $costs[$country][$industry] ?? $costs['GH']['services'];
}

/**
 * Get funding sources for a specific country
 */
function chatgabi_get_funding_sources($country) {
    $sources = array(
        'GH' => array('Ghana EXIM Bank', 'Venture Capital Trust Fund', 'National Board for Small Scale Industries', 'Microfinance institutions'),
        'KE' => array('Kenya Development Bank', 'Youth Enterprise Development Fund', 'Women Enterprise Fund', 'Angel investors'),
        'NG' => array('Bank of Industry', 'NIRSAL Microfinance Bank', 'Tony Elumelu Foundation', 'Lagos Angel Network'),
        'ZA' => array('Industrial Development Corporation', 'Small Enterprise Finance Agency', 'Business Partners Limited', 'Angel investors')
    );

    return $sources[$country] ?? $sources['GH'];
}
